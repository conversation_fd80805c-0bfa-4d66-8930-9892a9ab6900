using HCloud.Core.Common;
using HCloud.Core.HCPlatform.Auth.Internal;
using HCloud.Core.HCPlatform.Ioc;
using HCloud.Core.HCPlatform.Multilingual;
using HCloud.Core.ProxyGenerator;
using HCloud.Core.ProxyGenerator.Models;
using HCloud.Core.Sugar;
using HCloud.ERP.IModuleServices.MES001WorkArea;
using HCloud.ERP.IModuleServices.MES001WorkArea.Models;
using HCloud.ERP.IModuleServices.COP001_SaleOrder;
using HCloud.ERP.IModuleServices.COP001_SaleOrder.Models;
using HCloud.ERP.IModuleServices.MES002_Craft;
using HCloud.ERP.IModuleServices.MES002_Craft.Models;
using HCloud.ERP.IModuleServices.MES003_WorkOrder.Models;
using HCloud.ERP.IModuleServices.MES005_CraftSchedule.Models;
using HCloud.ERP.IModuleServices.MES007_JobBooking;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HCloud.ERP.IModuleServices.MSD002_Material.Models;
using HCloud.ERP.IModuleServices.MSD002_Material;
using HCloud.Core.HCPlatform.Serialization;
using HCloud.Core.HCPlatform.CommonData;
using HCloud.ERP.IModuleServices.MSD001_Unit.Models;
using HCloud.ERP.IModuleServices.MSD001_Unit;
using HCloud.ERP.IModuleServices.ADM024_Employee;
using HCloud.ERP.IModuleServices.ADM024_Employee.Models;
using HCloud.ERP.IModuleServices.MES006_WorkIn.Models;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using HCloud.ERP.IModuleServices.MES006_WorkIn;
using HCloud.ERP.IModuleServices.STK005_Stock.Models;
using HCloud.ERP.IModuleServices.STK005_Stock;
using HCloud.ERP.IModuleServices.SYS000_Common.Services;
using HCloud.ERP.IModuleServices.MES007_JobBooking.Models.Request;
using HCloud.Core.HCPlatform.ThirdParty;
using System.Net.Http;

namespace HCloud.ERP.ModuleServices.MES007_JobBooking
{
    [ModuleName("MES007JobBooking")]
    public partial class MES007JobBookingService : ProxyServiceBase, IMES007JobBookingService
    {
        private readonly ILogger _Logger = null;
        private readonly IAuth _iauth = null;
        private readonly ISugar<MES007JobBookingService> _isugar = null;
        private readonly IMultilingualResource _multiLang = null;
        private readonly ISerializer<string> _serialize = null;
        private readonly ICommonDataProvider _commonDataProvider = null;
        private readonly IBusinessDataService _businessService = null;
        private readonly IHttpClientFactory _httpClient = null;
        //注入获取参数
        private readonly IThirdPartySetting _ThirdPartySetting = null;

        public MES007JobBookingService(ILogger<MES007JobBookingService> logger,
             ISugar<MES007JobBookingService> sugar, IAuth auth, IMultilingualResource multiLang,
             ISerializer<string> serialize, ICommonDataProvider commonDataProvider, IBusinessDataService businessService, IThirdPartySetting ThirdPartySetting, IHttpClientFactory httpClient)
        {
            _Logger = logger;
            _iauth = auth;
            _isugar = sugar;
            _multiLang = multiLang;
            _serialize = serialize;
            _commonDataProvider = commonDataProvider;
            _businessService = businessService;
            _ThirdPartySetting = ThirdPartySetting;
            _httpClient = httpClient;
        }


        /// <summary>
        /// 查询工单工艺加工信息
        /// </summary>

        public async Task<DataResult<List<CraftJobBookingModel>>> QueryOrderJOBAsync(List<string> ids)
        {
            var db = _isugar.DB;


            var sche = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => ids.Contains(p.FWORK_ORDER_ID) && p.FWORK_STATUS != "cancel").ToListAsync();

            var schesum = sche.MapToDestObj<T_MESD_CRAFT_JOB_BOOKING, CraftJobBookingModel>();

            //result         
            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>
            {
                Entity = schesum,
                StatusCode = 200,
            };
            return await OK(result);

        }



        /// <summary>
        ///根据工位查询 工单ids
        /// </summary>

        public async Task<DataResult<List<string>>> QueryWOidsByStationAsync(List<string> ids)
        {
            var db = _isugar.DB;


            var sche = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => ids.Contains(p.FSTATION_ID)).Select(p => p.FWORK_ORDER_ID).Distinct().ToListAsync();



            //result         
            DataResult<List<string>> result = new DataResult<List<string>>
            {
                Entity = sche,
                StatusCode = 200,
            };
            return await OK(result);

        }


        #region 查询接口
        /// <summary>
        /// 查询工艺排程任务, 返回排程任务及进行中的加工任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<CraftScheduleJobModel>> QueryCraftScheduleJobAsync(CraftScheduleJobQueryModel model)
        {
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 100001, string.Format(_multiLang["传入参数 {0} 为空."], "model"));
            }

            if (string.IsNullOrWhiteSpace(model.FQRCODE))
            {
                ERROR(null, 100002, string.Format(_multiLang["传入二维码参数 {0} 为空."], "model.FQRCODE"));
            }

            //按工位找出工位Id
            if (!string.IsNullOrWhiteSpace(model.FSTATION) && string.IsNullOrWhiteSpace(model.FSTATION_ID))
            {
                string stationId = await QueryWorkStationIdAsync(model.FSTATION);
                if (string.IsNullOrWhiteSpace(stationId))
                {
                    ERROR(null, 100610, string.Format(_multiLang["查找工位 {0} 失败."], model.FSTATION));
                }
                model.FSTATION_ID = stationId;
            }

            var db = _isugar.DB;
            //查询计划任务数据
            CraftScheduleJobModel jobModel = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((sch, schStatus, wo, woStatus) =>
                  new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                        JoinType.Left, wo.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID))
                .Where((sch, schStatus, wo, woStatus) => sch.FQRCODE == model.FQRCODE && wo.FBOOKING_TYPE == _wocraft)  //按工单工艺报工
                .Select((sch, schStatus, wo, woStatus) => new CraftScheduleJobModel
                {
                    FCRAFT_ID = sch.FCRAFT_ID,
                    FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,
                    FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,

                    FECODE = sch.FECODE,
                    FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,
                    FIF_CLOSE_SCHEDULE = schStatus.FIF_CLOSE,

                    FIF_CLOSE_WORK_ORDER = woStatus.FIF_CLOSE,
                    FMATERIAL_ID = sch.FMATERIAL_ID,
                    FPLAN_ED_DATE = sch.FPLAN_ED_DATE,

                    FPLAN_QTY = sch.FPLAN_QTY,
                    FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                    FPLAN_ST_DATE = sch.FPLAN_ST_DATE,
                    FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                    FQRCODE = sch.FQRCODE,
                    FSALE_ORDER_ID = sch.FSALE_ORDER_ID,
                    FSTATION_ID = sch.FSTATION_ID,

                    FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                    FRELEASE_STATUS = schStatus.FRELEASE_STATUS,

                    FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                    //重量
                    FPLAN_WEIGHT = sch.FPLAN_WEIGHT,
                    FFINISH_WEIGHT_SCHEDULE = schStatus.FFINISH_WEIGHT,

                    FUNIT_WEIGHT = wo.FUNIT_WEIGHT,
                    FWEIGHT_UNIT_ID = wo.FWEIGHT_UNIT_ID,

                    FIS_OUT = schStatus.FIS_OUT,

                    FMATERIAL_BOM_ID = wo.FMATERIAL_BOM_ID,
                    FCRAFT_LINE_ID = wo.FCRAFT_LINE_ID,

                    FLAYOUT_NUM = wo.FLAYOUT_NUM,
                    FSHEET_NUM = wo.FSHEET_NUM,
                })
                .FirstAsync();

            //验证
            if (jobModel == null)
            {
                ERROR(null, 100100, string.Format(_multiLang["二维码 {0} 排程任务信息不存在."], model.FQRCODE));
            }


            //验证下发状态
            if (!jobModel.FRELEASE_STATUS)
            {
                ERROR(null, 100101, string.Format(_multiLang["二维码 {0} 排程任务未下发."], model.FQRCODE));
            }

            //验证工单是否已结案(佳乐取消提示)
            //if (jobModel.FIF_CLOSE_WORK_ORDER == 1 || jobModel.FIF_CLOSE_WORK_ORDER == 3)
            //{
            //    ERROR(null, 100104, string.Format(_multiLang["二维码 {0} 排程任务对应工单 {1} 已结案."], model.FQRCODE, jobModel.FWORK_ORDER_NO));
            //}

            //验证排程任务是否已结案
            //if ((jobModel.FIF_CLOSE_SCHEDULE == 1 || jobModel.FIF_CLOSE_SCHEDULE == 3) && !model.FIS_OUT)
            //{
            //    ERROR(null, 100105, string.Format(_multiLang["二维码 {0} 排程任务已结案."], model.FQRCODE));
            //}


            //取出工位
            var stationResult = await GetStationByIdsAsync(new List<string> { jobModel.FSTATION_ID });
            if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
            {
                jobModel.FSTATION_CODE = stationResult.Entity[0].FSTATION_CODE;
                jobModel.FSTATION_NAME = stationResult.Entity[0].FSTATION_NAME;
                jobModel.FSTATION_PIC_ATTACH_ID = stationResult.Entity[0].FPIC_ATTACH_ID;
                jobModel.FBOOK_ID = stationResult.Entity[0].FBOOK_ID;
            }


            //返回扫码的工位Id
            if (!string.IsNullOrWhiteSpace(model.FSTATION_ID))
            {
                stationResult = await GetStationByIdsAsync(new List<string> { model.FSTATION_ID });
                if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
                {
                    jobModel.FSTATION_ID = stationResult.Entity[0].FSTATION_ID;
                    jobModel.FSTATION_CODE = stationResult.Entity[0].FSTATION_CODE;
                    jobModel.FSTATION_NAME = stationResult.Entity[0].FSTATION_NAME;
                    jobModel.FSTATION_PIC_ATTACH_ID = stationResult.Entity[0].FPIC_ATTACH_ID;
                    jobModel.FBOOK_ID = stationResult.Entity[0].FBOOK_ID;
                }
            }



            //取出工艺
            var craftResult = await GetCraftByIdsAsync(new List<string> { jobModel.FCRAFT_ID });
            if (craftResult.StatusCode == 200 && craftResult.Entity != null && craftResult.Entity.Count > 0)
            {
                jobModel.FCRAFT_CODE = craftResult.Entity[0].FCRAFT_CODE;
                jobModel.FCRAFT_NAME = craftResult.Entity[0].FCRAFT_NAME;
            }

            //取出产品信息
            var materialResult = await GetMaterialByIdsAsync(new List<string> { jobModel.FMATERIAL_ID });
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }
            if (materialResult.Entity != null && materialResult.Entity.Count > 0)
            {
                jobModel.FMATERIAL_CODE = materialResult.Entity[0].FMATERIAL_CODE;
                jobModel.FMATERIAL_NAME = materialResult.Entity[0].FMATERIAL_NAME;
                jobModel.FMATERIAL_PIC_ATTACH_ID = materialResult.Entity[0].FPIC_ATTACH_ID;
            }

            //重量单位
            List<string> unitIds = new List<string> { jobModel.FPRO_UNIT_ID };
            if (!string.IsNullOrWhiteSpace(jobModel.FWEIGHT_UNIT_ID))
            {
                unitIds.Add(jobModel.FWEIGHT_UNIT_ID);
            }

            //取出单位名称
            var unitResult = await RpcGetUnitsAsync(unitIds);
            if (unitResult.StatusCode == 200)
            {
                if (unitResult.Entity != null && unitResult.Entity.Count > 0)
                {
                    //数量单位
                    var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == jobModel.FPRO_UNIT_ID);
                    if (unit != null)
                    {
                        jobModel.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                        jobModel.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                    }

                    //重量单位
                    unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == jobModel.FWEIGHT_UNIT_ID);
                    if (unit != null)
                    {
                        jobModel.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                    }
                }
            }
            else
            {
                ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
            }

            //取出进行中的加工任务
            List<CraftJobBookingModel> jobModels = null;

            if (!model.FIS_OUT)
            {
                if (string.IsNullOrWhiteSpace(model.FSTATION_ID))
                {
                    //没有传工位，按当前用户匹配一次
                    var query = db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((job, sch, schStatus) => new JoinQueryInfos(
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                   .Where((job, sch, schStatus) => job.FCRAFT_SCHEDULE_ID == jobModel.FCRAFT_SCHEDULE_ID
                       && (job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                           job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                           job.FWORK_STATUS == WorkStatus.working.ToString()))
                   .Where((job, sch, schStatus) => job.FEMP_ID == user.UserPsnId);
                    jobModels = await QueryJobBookingsAsync(query);
                }
                if (!string.IsNullOrWhiteSpace(model.FSTATION_ID) || jobModels == null || jobModels.Count == 0)
                {
                    //按工位或无工位找
                    var query = db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((job, sch, schStatus) => new JoinQueryInfos(
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                   .Where((job, sch, schStatus) => job.FCRAFT_SCHEDULE_ID == jobModel.FCRAFT_SCHEDULE_ID
                       && (job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                           job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                           job.FWORK_STATUS == WorkStatus.working.ToString()))
                   .WhereIF(!string.IsNullOrWhiteSpace(model.FSTATION_ID), (job, sch, schStatus) => job.FSTATION_ID == model.FSTATION_ID);
                    jobModels = await QueryJobBookingsAsync(query);
                }

                //计算实际使用工时
                if (jobModels.Count > 0)
                {
                    jobModels.AsParallel().WithDegreeOfParallelism(4).ForAll(jobModel =>
                    {
                        jobModel.FACT_USE_HOUR = CalcActUseHour(jobModel.FACT_USE_HOUR, jobModel.FLAST_RESUME_DATE, jobModel.FACT_ST_DATE, _iauth.GetCurDateTime(), jobModel.FWORK_STATUS);
                    });

                    //若有加工中任务，则按加工中的任务工位作为扫码工位
                    stationResult = await GetStationByIdsAsync(jobModels.Select(p => p.FSTATION_ID).Distinct().ToList());
                    if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
                    {
                        jobModel.FSTATION_ID = stationResult.Entity[0].FSTATION_ID;
                        jobModel.FSTATION_CODE = stationResult.Entity[0].FSTATION_CODE;
                        jobModel.FSTATION_NAME = stationResult.Entity[0].FSTATION_NAME;
                        jobModel.FSTATION_PIC_ATTACH_ID = stationResult.Entity[0].FPIC_ATTACH_ID;
                    }
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(model.FSTATION_ID))
                {
                    //没有传工位，按当前用户匹配一次
                    var query = db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((job, sch, schStatus) => new JoinQueryInfos(
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                   .Where((job, sch, schStatus) => job.FCRAFT_SCHEDULE_ID == jobModel.FCRAFT_SCHEDULE_ID
                       && (job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                           job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                           job.FWORK_STATUS == WorkStatus.working.ToString() ||
                           job.FWORK_STATUS == WorkStatus.finished.ToString()))
                   .Where((job, sch, schStatus) => job.FEMP_ID == user.UserPsnId);
                    jobModels = await QueryJobBookingsAsync(query);
                }
                if (!string.IsNullOrWhiteSpace(model.FSTATION_ID) || jobModels == null || jobModels.Count == 0)
                {
                    //按工位或无工位找
                    var query = db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((job, sch, schStatus) => new JoinQueryInfos(
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                   .Where((job, sch, schStatus) => job.FCRAFT_SCHEDULE_ID == jobModel.FCRAFT_SCHEDULE_ID
                       && (job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                           job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                           job.FWORK_STATUS == WorkStatus.working.ToString() ||
                           job.FWORK_STATUS == WorkStatus.finished.ToString()))
                   .WhereIF(!string.IsNullOrWhiteSpace(model.FSTATION_ID), (job, sch, schStatus) => job.FSTATION_ID == model.FSTATION_ID);
                    jobModels = await QueryJobBookingsAsync(query);
                }

                //计算实际使用工时
                if (jobModels.Count > 0)
                {
                    jobModels.AsParallel().WithDegreeOfParallelism(4).ForAll(jobModel =>
                    {
                        jobModel.FACT_USE_HOUR = CalcActUseHour(jobModel.FACT_USE_HOUR, jobModel.FLAST_RESUME_DATE, jobModel.FACT_ST_DATE, _iauth.GetCurDateTime(), jobModel.FWORK_STATUS);
                    });

                    //若有加工中任务，则按加工中的任务工位作为扫码工位
                    stationResult = await GetStationByIdsAsync(jobModels.Select(p => p.FSTATION_ID).Distinct().ToList());
                    if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
                    {
                        jobModel.FSTATION_ID = stationResult.Entity[0].FSTATION_ID;
                        jobModel.FSTATION_CODE = stationResult.Entity[0].FSTATION_CODE;
                        jobModel.FSTATION_NAME = stationResult.Entity[0].FSTATION_NAME;
                        jobModel.FSTATION_PIC_ATTACH_ID = stationResult.Entity[0].FPIC_ATTACH_ID;
                        jobModel.FBOOK_ID = stationResult.Entity[0].FBOOK_ID;
                    }
                }
            }


            jobModel.HandlingJobs = jobModels;

            //查询上一工艺的信息

            var craftUp = jobModel.craftUpInfo;

            var workCrafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => p.FWORK_ORDER_ID == jobModel.FWORK_ORDER_ID).OrderBy(p => p.FSHOW_SEQNO).Select(p => p.FWORK_ORDER_CRAFT_ID).ToListAsync();

            var index = workCrafts.IndexOf(jobModel.FWORK_ORDER_CRAFT_ID);
            if (index > 0)
            {
                var craftupId = workCrafts[index - 1];
                var craftUpJobPassSum = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FWORK_ORDER_CRAFT_ID == craftupId && p.FWORK_STATUS == "finished").SumAsync(p => p.FPASS_QTY);

                var craftJobPassSum = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FWORK_ORDER_CRAFT_ID == jobModel.FWORK_ORDER_CRAFT_ID && p.FWORK_STATUS == "finished").SumAsync(p => p.FPASS_QTY);

                var CraftId = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => p.FWORK_ORDER_ID == jobModel.FWORK_ORDER_ID && p.FWORK_ORDER_CRAFT_ID == craftupId).Select(p => p.FCRAFT_ID).FirstAsync();
                if (!string.IsNullOrEmpty(CraftId))
                {
                    //取出工艺
                    var cResult = await GetCraftByIdsAsync(new List<string> { CraftId });
                    if (cResult.StatusCode == 200 && cResult.Entity != null && cResult.Entity.Count > 0)
                    {
                        craftUp.FCRAFT_CODE = craftResult.Entity[0].FCRAFT_CODE;
                        craftUp.FCRAFT_NAME = craftResult.Entity[0].FCRAFT_NAME;
                    }
                }

                craftUp.UpCountNum = craftUpJobPassSum;
                craftUp.CurrentCountNum = craftJobPassSum;


            }

            DataResult<CraftScheduleJobModel> result = new DataResult<CraftScheduleJobModel>
            {
                Entity = jobModel,
                StatusCode = 200,
            };

            return await OK(result);
        }


        /// <summary>
        /// 根据加工任务ID  获取加工状态
        /// </summary>
        /// <returns></returns>
        public async Task<DataResult<List<CraftJobBookingModel>>> GetJobBookingStatusAsync(List<string> ids)
        {
            var user = await _iauth.GetUserAccountAsync();

            var db = _isugar.DB;

            var dao = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => ids.Contains(p.FCRAFT_JOB_BOOKING_ID)).Select<CraftJobBookingModel>().ToListAsync();

            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>
            {
                Entity = dao,
                StatusCode = 200,
            };

            return await OK(result);
        }


        /// <summary>
        /// 根据加工id 获取加工信息
        /// </summary>
        public async Task<DataResult<CraftJobBookingDataModel>> QueryJobBookingAsync(string id)
        {
            var user = await _iauth.GetUserAccountAsync();

            var db = _isugar.DB;
            //查询计划任务数据
            var jobModel = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS, T_MESD_WORK_ORDER>
                ((a, b, c, d) =>
                  new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, b.FCRAFT_SCHEDULE_ID == c.FCRAFT_SCHEDULE_ID, JoinType.Left, a.FWORK_ORDER_ID == d.FWORK_ORDER_ID))
                .Where((a, b, c, d) => a.FCRAFT_JOB_BOOKING_ID == id)
                .Select((a, b, c, d) => new CraftJobBookingDataModel
                {
                    FPRO_UNIT_ID = d.FPRO_UNIT_ID,

                    FCRAFT_JOB_BOOKING_ID = a.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_ID = a.FCRAFT_ID,
                    FCRAFT_JOB_BOOKING_NO = a.FCRAFT_JOB_BOOKING_NO,

                    FFINISH_QTY_SCHEDULE = c.FFINISH_QTY,
                    FFINISH_WEIGHT_SCHEDULE = c.FFINISH_WEIGHT,

                    FPLAN_WEIGHT = b.FPLAN_WEIGHT,
                    FPLAN_QTY = b.FPLAN_QTY,


                    FPASS_QTY = a.FPASS_QTY,
                    FNG_QTY = a.FNG_QTY,
                    FPASS_WEIGHT = a.FPASS_WEIGHT,
                    FNG_WEIGHT = a.FNG_WEIGHT,

                    FMATERIAL_ID = a.FMATERIAL_ID,

                    FACT_ST_DATE = a.FACT_ST_DATE,
                    FACT_ED_DATE = a.FACT_ED_DATE,
                    FACT_USE_HOUR = a.FACT_USE_HOUR,

                    FSTATION_ID = a.FSTATION_ID,
                    FWEIGHT_UNIT_ID = b.FWEIGHT_UNIT_ID,

                    FUNIT_ID = d.FUNIT_ID,
                    FUNIT_WEIGHT = b.FUNIT_WEIGHT,
                    FEMP_NAME = a.FEMP_NAME
                })
                .FirstAsync();


            //取出工位
            var stationResult = await GetStationByIdsAsync(new List<string> { jobModel.FSTATION_ID });
            if (stationResult.StatusCode == 200 && stationResult.Entity != null && stationResult.Entity.Count > 0)
            {
                jobModel.FSTATION_CODE = stationResult.Entity[0].FSTATION_CODE;
                jobModel.FSTATION_NAME = stationResult.Entity[0].FSTATION_NAME;
            }

            //取出工艺
            var craftResult = await GetCraftByIdsAsync(new List<string> { jobModel.FCRAFT_ID });
            if (craftResult.StatusCode == 200 && craftResult.Entity != null && craftResult.Entity.Count > 0)
            {
                jobModel.FCRAFT_CODE = craftResult.Entity[0].FCRAFT_CODE;
                jobModel.FCRAFT_NAME = craftResult.Entity[0].FCRAFT_NAME;
            }

            //取出产品信息
            var materialResult = await GetMaterialByIdsAsync(new List<string> { jobModel.FMATERIAL_ID });
            if (materialResult.StatusCode != 200)
            {
                ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
            }
            if (materialResult.Entity != null && materialResult.Entity.Count > 0)
            {
                jobModel.FMATERIAL_CODE = materialResult.Entity[0].FMATERIAL_CODE;
                jobModel.FMATERIAL_NAME = materialResult.Entity[0].FMATERIAL_NAME;
                jobModel.FMATERIAL_PIC_ATTACH_ID = materialResult.Entity[0].FPIC_ATTACH_ID;
            }

            //重量单位
            List<string> unitIds = new List<string> { jobModel.FWEIGHT_UNIT_ID, jobModel.FUNIT_ID, jobModel.FPRO_UNIT_ID };

            //取出单位名称
            var unitResult = await RpcGetUnitsAsync(unitIds);
            if (unitResult.StatusCode == 200)
            {
                if (unitResult.Entity != null && unitResult.Entity.Count > 0)
                {
                    //数量单位
                    var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == jobModel.FUNIT_ID);
                    if (unit != null)
                    {
                        jobModel.FUNIT_CODE = unit.FUNIT_CODE;
                        jobModel.FUNIT_NAME = unit.FUNIT_NAME;
                    }

                    //重量单位
                    unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == jobModel.FWEIGHT_UNIT_ID);
                    if (unit != null)
                    {
                        jobModel.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                        jobModel.FWEIGHT_UNIT_CODE = unit.FUNIT_CODE;
                    }
                    //生产单位
                    unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == jobModel.FPRO_UNIT_ID);
                    if (unit != null)
                    {
                        jobModel.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                        jobModel.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                    }

                }
            }
            else
            {
                ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
            }



            DataResult<CraftJobBookingDataModel> result = new DataResult<CraftJobBookingDataModel>
            {
                Entity = jobModel,
                StatusCode = 200,
            };

            return await OK(result);
        }

        /// <summary>
        /// 修改加工数量
        /// </summary>
        public async Task<DataResult<UpdateJobBookingModel>> UpdateJobBookingAsync(UpdateJobBookingModel model)
        {
            var user = await _iauth.GetUserAccountAsync();
            var db = _isugar.DB;

            // ========================= 1. 事务外数据查询和验证 =========================

            // 1.1 查询要修改的加工任务记录
            var jobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => model.FCRAFT_JOB_BOOKING_ID == p.FCRAFT_JOB_BOOKING_ID).FirstAsync();
            if (jobData == null) ERROR(null, 111110, "找不到要修改的加工任务记录。");

            // 1.2 查询排程任务信息
            var scheduleInfo = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(s => s.FCRAFT_SCHEDULE_ID == jobData.FCRAFT_SCHEDULE_ID).FirstAsync();
            if (scheduleInfo == null) ERROR(null, 111111, "找不到报工记录关联的排程任务。");

            // 计算数量变化
            decimal quantityDelta = (model.FPASS_QTY + model.FNG_QTY) - (jobData.FPASS_QTY + jobData.FNG_QTY);

            // 1.3 超产验证
            var sysParamAllowOver = await GetSysParamValue(_MES_FinishQtyAllowGreatSch);
            if (sysParamAllowOver == "0")
            {
                var otherJobsQty = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FCRAFT_SCHEDULE_ID == jobData.FCRAFT_SCHEDULE_ID && p.FCRAFT_JOB_BOOKING_ID != model.FCRAFT_JOB_BOOKING_ID).SumAsync(p => p.FPASS_QTY + p.FNG_QTY);
                decimal newTotalQty = otherJobsQty + model.FPASS_QTY + model.FNG_QTY;
                if (newTotalQty > scheduleInfo.FPLAN_QTY)
                {
                    ERROR(null, 111112, $"保存失败：修改后的总完工数({newTotalQty})将超出排程任务[{scheduleInfo.FCRAFT_SCHEDULE_NO}]的计划数({scheduleInfo.FPLAN_QTY})。系统当前设置不允许超产。");
                }
            }

            // 1.4 下游任务状态验证
            var downstreamSchedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().Where(s => s.FSOURCE_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID).FirstAsync();
            if (downstreamSchedule != null)
            {
                bool isDownstreamStarted = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().AnyAsync(j => j.FCRAFT_SCHEDULE_ID == downstreamSchedule.FCRAFT_SCHEDULE_ID);
                if (isDownstreamStarted)
                {
                    ERROR(null, 111113, $"无法修改数量：此报工记录产生的下游排程任务[{downstreamSchedule.FCRAFT_SCHEDULE_NO}]已开始加工，为保证数据一致性，操作被禁止。");
                }
            }

            // 1.5 上游父排程数量验证 (处理当前任务是子任务的情况)
            T_MESD_CRAFT_SCHEDULE parentSchedule = null;
            if (!string.IsNullOrEmpty(scheduleInfo.FPARENT_CRAFT_SCHEDULE_ID))
            {
                if (quantityDelta != 0)
                {
                    parentSchedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().InSingleAsync(scheduleInfo.FPARENT_CRAFT_SCHEDULE_ID);
                    if (parentSchedule == null)
                    {
                        ERROR(null, 111114, $"数据异常：找不到子排程[{scheduleInfo.FCRAFT_SCHEDULE_NO}]的上游父排程记录。");
                    }
                    // 注意: quantityDelta为负数时，是归还数量，此时parentSchedule.FPLAN_QTY会增加，这个检查主要是防止增加扣减量
                    if (parentSchedule.FPLAN_QTY < quantityDelta)
                    {
                        ERROR(null, 111115, $"无法修改数量：此操作需要从上游父排程[{parentSchedule.FCRAFT_SCHEDULE_NO}]额外扣除 {quantityDelta} 的计划数，但其剩余计划数只有 {parentSchedule.FPLAN_QTY}，不足以扣减。");
                    }
                }
            }

            // 1.6 声明并查找下游任务的父排程，用于数量归还
            T_MESD_CRAFT_SCHEDULE downstreamParentSchedule = null;
            if (downstreamSchedule != null && !string.IsNullOrEmpty(downstreamSchedule.FPARENT_CRAFT_SCHEDULE_ID))
            {
                if (quantityDelta != 0)
                {
                    downstreamParentSchedule = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().InSingleAsync(downstreamSchedule.FPARENT_CRAFT_SCHEDULE_ID);
                    if (downstreamParentSchedule == null)
                    {
                        ERROR(null, 111116, $"数据异常：找不到下游排程[{downstreamSchedule.FCRAFT_SCHEDULE_NO}]的父排程记录。");
                    }
                }
            }

            // 1.7 验证逻辑
            if (jobData.FWORK_STATUS != "finished") ERROR(jobData, 111111, $"加工任务 {jobData.FCRAFT_JOB_BOOKING_NO} 未完工, 无法修改数量。");

            var workOrd = await db.Queryable<T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS>
                ((a, b) => new JoinQueryInfos(
                    JoinType.Left, a.FWORK_ORDER_ID == b.FWORK_ORDER_ID
                ))
                .Where((a, b) => a.FWORK_ORDER_ID == jobData.FWORK_ORDER_ID)
                .Select((a, b) => new { a.FWORK_ORDER_ID, a.FPRO_QTY, b.FIF_CLOSE, b.FWORK_ORDER_STATUS_ID })
                .FirstAsync();
            var workOrdCrafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>()
                .Where(p => p.FWORK_ORDER_ID == jobData.FWORK_ORDER_ID)
                .OrderBy(p => p.FSHOW_SEQNO, OrderByType.Desc)
                .FirstAsync();

            if (workOrd.FIF_CLOSE == 1 || workOrd.FIF_CLOSE == 3)
            {
                if (jobData.FWORK_ORDER_CRAFT_ID != workOrdCrafts.FWORK_ORDER_CRAFT_ID)
                {
                    ERROR(jobData, 111111, $"工单已结案，加工任务编号 {jobData.FCRAFT_JOB_BOOKING_NO} 不是最后一道工艺，无法修改。");
                }
            }

            if (jobData.FWORK_ORDER_CRAFT_ID == workOrdCrafts.FWORK_ORDER_CRAFT_ID)
            {
                var workInDatas = await db.Queryable<T_MESD_WORK_IN_PRODUCT, T_MESD_WORK_IN, T_MESD_WORK_IN_STATUS>
                    ((a, b, c) => new JoinQueryInfos(
                        JoinType.Left, a.FWORK_IN_ID == b.FWORK_IN_ID, 
                        JoinType.Left, b.FWORK_IN_ID == c.FWORK_IN_ID))
                    .Where((a, b, c) => model.FCRAFT_JOB_BOOKING_ID == a.FCRAFT_JOB_BOOKING_ID && c.FCFLAG == 1)
                    .Select((a, b, c) => new { 
                        a.FCRAFT_JOB_BOOKING_ID, 
                        a.FCRAFT_JOB_BOOKING_NO, 
                        b.FWORK_IN_NO, 
                        c.FCFLAG 
                    })
                    .FirstAsync();
                if (workInDatas != null)
                {
                    ERROR(workInDatas, 111111, $"加工任务单 {workInDatas.FCRAFT_JOB_BOOKING_NO} 关联的工单入库单 {workInDatas.FWORK_IN_NO} 已审核，无法修改。");
                }
            }

            // ========================= 2. 事务外查询所有需要的数据 =========================
            var craftData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => p.FCRAFT_SCHEDULE_ID == jobData.FCRAFT_SCHEDULE_ID)
                .ToListAsync();
            
            var craftInfo = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                ((a, b) => new JoinQueryInfos(JoinType.Left, a.FCRAFT_SCHEDULE_ID == b.FCRAFT_SCHEDULE_ID))
                .Where((a, b) => jobData.FCRAFT_SCHEDULE_ID == a.FCRAFT_SCHEDULE_ID)
                .Select((a, b) => new { 
                    b.FCRAFT_SCHEDULE_STATUS_ID,
                    a.FCRAFT_SCHEDULE_ID,
                    a.FPLAN_QTY,
                    b.FIF_CLOSE 
                })
                .FirstAsync();

            var workOrdCraftData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => p.FWORK_ORDER_ID == jobData.FWORK_ORDER_ID && p.FWORK_ORDER_CRAFT_ID == jobData.FWORK_ORDER_CRAFT_ID).ToListAsync();
            var workOrdCraftInfo = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>().Where(p => p.FWORK_ORDER_CRAFT_ID == jobData.FWORK_ORDER_CRAFT_ID && p.FWORK_ORDER_ID == jobData.FWORK_ORDER_ID).FirstAsync();
            T_MESD_WORK_ORDER_CRAFT_STATUS workOrdCraftStatu = null;
            if (workOrdCrafts.FWORK_ORDER_CRAFT_ID == jobData.FWORK_ORDER_CRAFT_ID)
            {
                workOrdCraftStatu = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>().Where(p => p.FWORK_ORDER_CRAFT_ID == workOrdCrafts.FWORK_ORDER_CRAFT_ID).FirstAsync();
            }
            var workInOld = await db.Queryable<T_MESD_WORK_IN_PRODUCT>().Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID).FirstAsync();

            // ========================= 3. 在内存中计算新的状态值 =========================
            var logdata = JsonConvert.SerializeObject(jobData);
            var modifyData = JsonConvert.SerializeObject(model);
            var craftStatu = new T_MESD_CRAFT_SCHEDULE_STATUS { FCRAFT_SCHEDULE_STATUS_ID = craftInfo.FCRAFT_SCHEDULE_STATUS_ID, FIF_CLOSE = craftInfo.FIF_CLOSE };
            var updatedCraftData = craftData.Select(p => new T_MESD_CRAFT_JOB_BOOKING { FCRAFT_JOB_BOOKING_ID = p.FCRAFT_JOB_BOOKING_ID, FPASS_QTY = p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID ? model.FPASS_QTY : p.FPASS_QTY, FNG_QTY = p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID ? model.FNG_QTY : p.FNG_QTY, FPASS_WEIGHT = p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID ? model.FPASS_WEIGHT : p.FPASS_WEIGHT, FNG_WEIGHT = p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID ? model.FNG_WEIGHT : p.FNG_WEIGHT }).ToList();
            craftStatu.FPASS_QTY = updatedCraftData.Sum(p => p.FPASS_QTY);
            craftStatu.FNG_QTY = updatedCraftData.Sum(p => p.FNG_QTY);
            craftStatu.FPASS_WEIGHT = updatedCraftData.Sum(p => p.FPASS_WEIGHT);
            craftStatu.FNG_WEIGHT = updatedCraftData.Sum(p => p.FNG_WEIGHT);
            craftStatu.FFINISH_QTY = craftStatu.FPASS_QTY + craftStatu.FNG_QTY;
            craftStatu.FFINISH_WEIGHT = craftStatu.FPASS_WEIGHT + craftStatu.FNG_WEIGHT;
            if (craftInfo.FPLAN_QTY > craftStatu.FFINISH_QTY) { if (craftInfo.FIF_CLOSE == 1 || craftInfo.FIF_CLOSE == 3) craftStatu.FIF_CLOSE = 2; }
            var updatedWorkOrdCraftData = workOrdCraftData.Select(p => new T_MESD_CRAFT_JOB_BOOKING { FCRAFT_JOB_BOOKING_ID = p.FCRAFT_JOB_BOOKING_ID, FPASS_QTY = p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID ? model.FPASS_QTY : p.FPASS_QTY, FNG_QTY = p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID ? model.FNG_QTY : p.FNG_QTY }).ToList();
            workOrdCraftInfo.FFINISH_QTY = updatedWorkOrdCraftData.Sum(p => p.FPASS_QTY + p.FNG_QTY);
            var workStatu = new T_MESD_WORK_ORDER_STATUS { FWORK_ORDER_STATUS_ID = workOrd.FWORK_ORDER_STATUS_ID, FIF_CLOSE = workOrd.FIF_CLOSE };
            if (workOrdCrafts.FWORK_ORDER_CRAFT_ID == jobData.FWORK_ORDER_CRAFT_ID && workOrdCraftStatu != null) { workStatu.FFINISH_QTY = workOrdCraftInfo.FFINISH_QTY; if (workOrd.FPRO_QTY > workStatu.FFINISH_QTY) { if (workOrd.FIF_CLOSE == 1 || workOrd.FIF_CLOSE == 3) workStatu.FIF_CLOSE = 2; } }
            decimal qty = 0; decimal funitQty = 0; decimal fstkQty = 0;
            if (workInOld != null) { qty = model.FPASS_QTY + model.FNG_QTY; funitQty = qty * workInOld.FUNIT_PRO_CONVERT_SCALE; fstkQty = workInOld.FUNIT_STK_CONVERT_SCALE == 0 ? 0 : funitQty / workInOld.FUNIT_STK_CONVERT_SCALE; }
            RecordStockBillModel recordData = null;
            if (workInOld != null) { recordData = await RecordStockBillAsync(new List<string>() { workInOld.FWORK_IN_ID }, IModuleServices.STK005_Stock.Models.OperateType.save, db); }


            // ========================= 4. 事务内批量更新操作 =========================

            db.BeginTran();
            try
            {
                // 4.1 更新当前报工记录
                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>()
                    .SetColumns(p => new T_MESD_CRAFT_JOB_BOOKING() { FPASS_QTY = model.FPASS_QTY, FNG_QTY = model.FNG_QTY, FPASS_WEIGHT = model.FPASS_WEIGHT, FNG_WEIGHT = model.FNG_WEIGHT })
                    .Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                    .ExecuteCommandAsync();

                // 4.2 更新下游任务（如果存在）
                if (downstreamSchedule != null)
                {
                    decimal newDownstreamQty = model.FPASS_QTY;
                    await db.Updateable<T_MESD_CRAFT_SCHEDULE>()
                        .SetColumns(it => new T_MESD_CRAFT_SCHEDULE { FPLAN_QTY = newDownstreamQty, FORI_PLAN_QTY = newDownstreamQty })
                        .Where(it => it.FCRAFT_SCHEDULE_ID == downstreamSchedule.FCRAFT_SCHEDULE_ID)
                        .ExecuteCommandAsync();
                }

                // 4.3 更新上游父排程（如果当前任务是子任务）
                if (parentSchedule != null && quantityDelta != 0)
                {
                    parentSchedule.FPLAN_QTY -= quantityDelta;
                    await db.Updateable(parentSchedule).UpdateColumns(it => it.FPLAN_QTY).ExecuteCommandAsync();
                }

                // NEW: 4.4 更新下游父排程（归还或扣减因本次修改而产生的差异数量）
                if (downstreamParentSchedule != null && quantityDelta != 0)
                {
                    // quantityDelta 为负数时，相当于增加父排程的计划数（归还）
                    downstreamParentSchedule.FPLAN_QTY -= quantityDelta;
                    await db.Updateable(downstreamParentSchedule).UpdateColumns(it => it.FPLAN_QTY).ExecuteCommandAsync();
                }

                // 4.5 更新排程任务状态
                await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(craftStatu).UpdateColumns(p => new { p.FIF_CLOSE, p.FPASS_QTY, p.FNG_QTY, p.FPASS_WEIGHT, p.FNG_WEIGHT, p.FFINISH_QTY, p.FFINISH_WEIGHT }).ExecuteCommandAsync();

                // 4.6 更新工单工艺状态
                await db.Updateable<T_MESD_WORK_ORDER_CRAFT_STATUS>(workOrdCraftInfo).UpdateColumns(p => new { p.FFINISH_QTY }).ExecuteCommandAsync();

                // 4.7 更新工单状态（如果是最后一道工艺）
                if (workOrdCrafts.FWORK_ORDER_CRAFT_ID == jobData.FWORK_ORDER_CRAFT_ID)
                {
                    await db.Updateable<T_MESD_WORK_ORDER_STATUS>(workStatu).UpdateColumns(p => new { p.FIF_CLOSE, p.FFINISH_QTY }).ExecuteCommandAsync();

                    // 4.8 更新工单入库产品（如果存在）
                    if (workInOld != null)
                    {
                        await db.Updateable<T_MESD_WORK_IN_PRODUCT>()
                            .SetColumns(p => new T_MESD_WORK_IN_PRODUCT() { FQTY = qty, FAMT = workInOld.FUP * qty, FUNIT_QTY = funitQty, FSTK_UNIT_QTY = fstkQty })
                            .Where(p => p.FCRAFT_JOB_BOOKING_ID == model.FCRAFT_JOB_BOOKING_ID)
                            .ExecuteCommandAsync();

                        // 4.9 登记库存作业（如果需要）
                        if (recordData != null && recordData.RecordModels.Count > 0)
                        {
                            var record = await RecordStockBillRpcAsync(recordData);
                            if (!record.Entity)
                            {
                                db.RollbackTran();
                                ERROR(record, 111111, $"工单入库单登记库存作业失败");
                            }
                        }
                    }
                }

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            // ========================= 5. 记录操作日志 =========================
            SaveOperateLogAsync(jobData, IModuleServices.MES007_JobBooking.Models.OperateType.modifyfinish, $"{logdata}--modifyed:{modifyData}");

            DataResult<UpdateJobBookingModel> result = new DataResult<UpdateJobBookingModel> { Entity = model, StatusCode = 200 };
            return await OK(result);
        }

        /// <summary>
        /// 获取当前用户部门信息
        /// </summary>
        /// <returns></returns>
        private async Task<DataResult<List<EmployeeModel>>> QueryDepUserInfo()
        {
            var user = await _iauth.GetUserAccountAsync();

            //获取部门id
            var deplModel = new QueryRequestModel()
            {
                WhereGroup = new QueryWhereGroupModel()
                {
                    GroupType = EnumGroupType.AND,
                    Items = new List<QueryWhereItemModel>()
                },
                PageIndex = 1,
                PageSize = 99999,
            };
            deplModel.WhereGroup.Items.Add(new QueryWhereItemModel()
            {
                FieldName = "a.FEMP_ID",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Equal,
                Value = user.UserPsnId,
            });
            var deprcp = this.GetService<IEmployeeService>("ADM024Employee");
            return await deprcp.GetAllEmpAsync(deplModel);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="station"></param>
        /// <returns></returns>
        private async Task<string> QueryWorkStationIdAsync(string station)
        {

            if (station.IndexOf("/") > -1)
            {
                station = station.Substring(0, station.IndexOf("/"));
            }

            var rpcServer = this.GetService<IModuleServices.MES001WorkArea.IMES001StationService>("MES001Station");
            QueryRequestModel requestModel = new QueryRequestModel
            {
                WhereGroup = new QueryWhereGroupModel
                {
                    Items = new List<QueryWhereItemModel>(),
                    GroupType = EnumGroupType.AND
                },
                PageIndex = 1,
                PageSize = int.MaxValue,
            };
            requestModel.WhereGroup.Items.Add(new QueryWhereItemModel
            {
                FieldName = "FSTATION_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Equal,
                Value = station,
            });

            var stationResult = await rpcServer.GetStationIDSAsync(requestModel);
            if (stationResult.StatusCode != 200)
            {
                ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
            }

            if (stationResult.Entity.Count != 1)
            {
                requestModel.WhereGroup.Items.Clear();
                requestModel.WhereGroup.GroupType = EnumGroupType.AND;
                requestModel.WhereGroup.Items.Add(new QueryWhereItemModel
                {
                    FieldName = "FSTATION_NAME",
                    OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Equal,
                    Value = station,
                });
                stationResult = await rpcServer.GetStationIDSAsync(requestModel);
                if (stationResult.StatusCode != 200)
                {
                    ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                }

            }

            if (stationResult.Entity.Count != 1)
            {
                requestModel.WhereGroup.Items.Clear();
                requestModel.WhereGroup.GroupType = EnumGroupType.OR;
                requestModel.WhereGroup.Items.Add(new QueryWhereItemModel
                {
                    FieldName = "FSTATION_CODE",
                    OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                    Value = station,
                });
                requestModel.WhereGroup.Items.Add(new QueryWhereItemModel
                {
                    FieldName = "FSTATION_NAME",
                    OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                    Value = station,
                });
                stationResult = await rpcServer.GetStationIDSAsync(requestModel);
                if (stationResult.StatusCode != 200)
                {
                    ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                }

            }

            if (stationResult.Entity.Count != 1)
            {
                ERROR(null, 100310, string.Format(_multiLang["无效工位 {0}"], station));
            }

            return stationResult.Entity[0];


        }

        /// <summary>
        /// 查询加工中任务
        /// </summary>
        /// <param name="queryable"></param>
        /// <returns></returns>
        private async Task<List<CraftJobBookingModel>> QueryJobBookingsAsync(ISugarQueryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS> queryable)
        {
            var model = await queryable
                .OrderBy((job, sch, schStatus) => job.FCDATE, OrderByType.Desc)
                .Select((job, sch, schStatus) => new CraftJobBookingModel
                {
                    FACT_ED_DATE = job.FACT_ED_DATE,
                    FACT_ST_DATE = job.FACT_ST_DATE,

                    FACT_USE_HOUR = job.FACT_USE_HOUR,

                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                    FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                    FEMP_ID = job.FEMP_ID,

                    FEMP_NAME = job.FEMP_NAME,
                    FLAST_PAUSE_DATE = job.FLAST_PAUSE_DATE,

                    FLAST_RESUME_DATE = job.FLAST_RESUME_DATE,
                    FNG_QTY = job.FNG_QTY,

                    FPASS_QTY = job.FPASS_QTY,
                    FWORK_STATUS = job.FWORK_STATUS,

                    FCRAFT_ID = job.FCRAFT_ID,
                    FMATERIAL_ID = job.FMATERIAL_ID,

                    FSALE_ORDER_ID = job.FSALE_ORDER_ID,
                    FSTATION_ID = job.FSTATION_ID,

                    FWORK_ORDER_CRAFT_ID = job.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_ID = job.FWORK_ORDER_ID,

                    FNG_WEIGHT = job.FNG_WEIGHT,
                    FPASS_WEIGHT = job.FPASS_WEIGHT,

                    FPLAN_QTY_SCHEDULE = sch.FPLAN_QTY,
                    FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,
                })
             .FirstAsync();

            if (model == null)
            {
                return new List<CraftJobBookingModel>();
            }
            else
            {
                return new List<CraftJobBookingModel>() { model };
            }

        }


        /// <summary>
        /// 计算实际使用工时
        /// </summary>
        /// <param name="oriActUseHour"></param>
        /// <param name="lastResumeDate"></param>
        /// <param name="startDate"></param>
        /// <param name="workStatus"></param>
        /// <returns></returns>
        private decimal CalcActUseHour(decimal oriActUseHour, DateTime? lastResumeDate, DateTime? startDate, DateTime curDate, string workStatus)
        {
            decimal result = oriActUseHour;
            if (workStatus == WorkStatus.working.ToString() || workStatus == WorkStatus.partfinished.ToString())
            {  //工作中
                if (lastResumeDate.HasValue)
                {
                    result = oriActUseHour + Math.Round(curDate.Subtract(lastResumeDate.Value).TotalHours.ObjToDecimal(), 6, MidpointRounding.ToEven);
                }
                else
                {
                    result = Math.Round(curDate.Subtract(startDate.Value).TotalHours.ObjToDecimal(), 6, MidpointRounding.ToEven);
                }
            }
            else   //已暂停,已完工,已取消
            {
                result = oriActUseHour;
            }
            return result;
        }

        /// <summary>
        /// 获取工艺信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleCraftModel>>> GetCraftByIdsAsync(List<string> craftIds)
        {
            var rpcServer = this.GetService<IMES002CraftService>("MES002Craft");
            return await rpcServer.GetForBusinessByIdsAsync(craftIds);
        }

        /// <summary>
        /// 返回工位数据,用于其他程序调用
        /// </summary>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleStationModel>>> GetStationByIdsAsync(List<string> stationIds)
        {
            var rpcServer = this.GetService<IMES001StationService>("MES001Station");
            return await rpcServer.GetForBusinessByIdsAsync(stationIds);

        }

        /// <summary>
        /// 获取简化版列表数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleMaterialModel>>> GetMaterialByIdsAsync(List<string> materialIds)
        {
            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            return await rpcServer.GetForBusinessByIdsAsync(materialIds);
        }




        /// <summary>
        /// 查询待加工排程任务--悠悠工位机排程  去掉已完成的排程信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> QueryWaitJobsIUIUAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 100001, string.Format(_multiLang["传入参数 {0} 为空."], "model"));
            }

            //总行数 
            RefAsync<int> totalSize = new RefAsync<int>();

            //清除传入字段为空的, MES003报表服务调此接口，传了空字段。MES003暂不能更新给鑫海，先在这里修改
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                model.WhereGroup.Items.RemoveAll(p => string.IsNullOrWhiteSpace(p.FieldName));
            }

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            /*
            string stationId = string.Empty;
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                var stationQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSTATION_ID");
                if (stationQuery != null)
                {
                    stationId = stationQuery.Value;
                }
            }*/

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             //.WhereIF(!string.IsNullOrWhiteSpace(stationId), (sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FSTATION_ID == stationId)             
             .Where((sch, schStatus, wo, woStatus, craft, craftStatus) => (schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2) && schStatus.FRELEASE_STATUS == true
                                                                           //&&sch.FPLAN_QTY > schStatus.FFINISH_QTY
                                                                           )

             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => schStatus.FACT_ST_DATE, OrderByType.Desc)
             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FPLAN_ST_DATE, OrderByType.Asc)

             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FREMARK = craft.FREMARK,
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 //重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,
                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,

                 SCHEDULE_FSHOW_SEQNO = sch.SCHEDULE_FSHOW_SEQNO,

                 FIUIU_CUST = wo.FIUIU_CUST,
                 FIUIU_ORDER_DATE = wo.FIUIU_ORDER_DATE,
                 FIUIU_ORDER_NO = wo.FIUIU_ORDER_NO,
                 FIUIU_SALE_NAME = wo.FIUIU_SALE_NAME




             })
               .Where(wheres)
             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                {
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == craft.FMATERIAL_ID);
                    if (material != null)
                    {
                        craft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        craft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        craft.FSPEC_DESC = material.FSPEC_DESC;
                        craft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        craft.FGOODS_MODEL = material.FGOODS_MODEL;
                    }
                });
                var saleOrderIds = woCraftModels.Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
                var saleOrdrpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                var saleOrderResult = await saleOrdrpcServer.QuerySalesByIdsAsync(saleOrderIds);
                if (saleOrderResult.StatusCode != 200)
                {
                    ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                }
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                {
                    var saleOrd = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == craft.FSALE_ORDER_ID);
                    if (saleOrd != null)
                    {
                        craft.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                        craft.FPROJECT_ID = saleOrd.FPROJECT_ID;  //项目
                        craft.FPROJECT_NAME = saleOrd.FPROJECT_NAME;
                        craft.FPROJECT_CODE = saleOrd.FPROJECT_CODE;
                    }
                });

                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                if (empIds.Count > 0)
                {
                    var empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                        {
                            if (!string.IsNullOrWhiteSpace(craft.FPLAN_EMP_ID))
                            {
                                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == craft.FPLAN_EMP_ID);
                                if (emp != null)
                                {
                                    craft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPRO_UNIT_ID)).Select(p => p.FPRO_UNIT_ID).Distinct().ToList();

                //重量单位
                unitIds.AddRange(woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FWEIGHT_UNIT_ID))
                                .Select(p => p.FWEIGHT_UNIT_ID).Distinct().ToList());

                if (unitIds.Count > 0)
                {
                    var unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                        {
                            //数量单位
                            if (!string.IsNullOrWhiteSpace(craft.FPRO_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FPRO_UNIT_ID);
                                if (unit != null)
                                {
                                    craft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                    craft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                }
                            }

                            //重量单位
                            if (!string.IsNullOrWhiteSpace(craft.FWEIGHT_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FWEIGHT_UNIT_ID);
                                if (unit != null)
                                {
                                    craft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                if (craftIds.Count > 0)
                {
                    var craftResult = await RpcGetCraftsAsync(craftIds);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }

                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    });
                }

                //取出工位
                var stationIds = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                if (stationIds.Count > 0)
                {
                    var stationResult = await RpcGetStationsAsync(stationIds);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (craft != null)
                        {
                            woCraft.FSTATION_CODE = craft.FSTATION_CODE;
                            woCraft.FSTATION_NAME = craft.FSTATION_NAME;
                        }
                    });
                }

                //取出投入物料
                await GetSubMaterialAsync(woCraftModels);

                //取出执行人
                await GetScheduleEmpsAsync(woCraftModels);



                //获取排程的加工状态
                await GetScheduleStatusAsync(woCraftModels);
            }

            DataResult<object> result = new DataResult<object>
            {
                Entity = woCraftModels.OrderBy(p => p.SCHEDULE_FSHOW_SEQNO).OrderBy(p => p.FMATERIAL_CODE).ToList(),

                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = totalSize,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };

            return await OK(result);
        }



        /// <summary>
        /// 查询待加工排程任务--悠悠工位机排程  去掉已完成的排程信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> QueryWaitJobsJINJIAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 100001, string.Format(_multiLang["传入参数 {0} 为空."], "model"));
            }

            //总行数 
            RefAsync<int> totalSize = new RefAsync<int>();

            //清除传入字段为空的, MES003报表服务调此接口，传了空字段。MES003暂不能更新给鑫海，先在这里修改
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                model.WhereGroup.Items.RemoveAll(p => string.IsNullOrWhiteSpace(p.FieldName));
            }

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);


            //查询
            var woCraftModelAll = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             //.WhereIF(!string.IsNullOrWhiteSpace(stationId), (sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FSTATION_ID == stationId)             
             .Where((sch, schStatus, wo, woStatus, craft, craftStatus) => (schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2) && schStatus.FRELEASE_STATUS == true
                                                                           //&&sch.FPLAN_QTY > schStatus.FFINISH_QTY
                                                                           )

             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => schStatus.FACT_ST_DATE, OrderByType.Desc)
             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FPLAN_ST_DATE, OrderByType.Asc)

             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FREMARK = craft.FREMARK,
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 //重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,
                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,

                 SCHEDULE_FSHOW_SEQNO = sch.SCHEDULE_FSHOW_SEQNO,
             })
               .Where(wheres)
             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            var workOrdIds = woCraftModelAll.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
            var craftSchIds = woCraftModelAll.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            var allStationIds = woCraftModelAll.Select(p => p.FSTATION_ID).Distinct().ToList();

            var woCraftModels = new List<WOCraftScheduleModel>();

            if (woCraftModelAll.Count > 0)
            {

                //检查前一工艺是否有完工记录
                var woCrafts = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>
                    ((craft, status) => new JoinQueryInfos(JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == status.FWORK_ORDER_CRAFT_ID))
                    .Where((craft, status) => workOrdIds.Contains(craft.FWORK_ORDER_ID))
                    .Select((craft, status) => new { craft.FWORK_ORDER_ID, craft.FWORK_ORDER_CRAFT_ID, craft.FCRAFT_ID, craft.FSHOW_SEQNO, status.FFINISH_QTY })
                    .ToListAsync())
                    .OrderBy(p => p.FWORK_ORDER_ID)
                    .OrderBy(p => p.FSHOW_SEQNO).ToList();

                var jobDatas = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((job, sch, schStatus) => new JoinQueryInfos(
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                   .Where((job, sch, schStatus) => craftSchIds.Contains(job.FCRAFT_SCHEDULE_ID) && allStationIds.Contains(job.FSTATION_ID)
                       && (job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                           job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                           job.FWORK_STATUS == WorkStatus.working.ToString() ||
                           job.FWORK_STATUS == WorkStatus.finished.ToString()))
                   .OrderBy((job, sch, schStatus) => job.FCDATE, OrderByType.Desc)
                   .Select((job, sch, schStatus) => new CraftJobBookingModel
                   {
                       FCDATE = job.FCDATE,
                       FACT_ED_DATE = job.FACT_ED_DATE,
                       FACT_ST_DATE = job.FACT_ST_DATE,

                       FACT_USE_HOUR = job.FACT_USE_HOUR,

                       FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                       FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                       FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                       FEMP_ID = job.FEMP_ID,

                       FEMP_NAME = job.FEMP_NAME,
                       FLAST_PAUSE_DATE = job.FLAST_PAUSE_DATE,

                       FLAST_RESUME_DATE = job.FLAST_RESUME_DATE,
                       FNG_QTY = job.FNG_QTY,

                       FPASS_QTY = job.FPASS_QTY,
                       FWORK_STATUS = job.FWORK_STATUS,

                       FCRAFT_ID = job.FCRAFT_ID,
                       FMATERIAL_ID = job.FMATERIAL_ID,

                       FSALE_ORDER_ID = job.FSALE_ORDER_ID,
                       FSTATION_ID = job.FSTATION_ID,

                       FWORK_ORDER_CRAFT_ID = job.FWORK_ORDER_CRAFT_ID,
                       FWORK_ORDER_ID = job.FWORK_ORDER_ID,

                       FNG_WEIGHT = job.FNG_WEIGHT,
                       FPASS_WEIGHT = job.FPASS_WEIGHT,

                       FPLAN_QTY_SCHEDULE = sch.FPLAN_QTY,
                       FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,
                   }).ToListAsync();

                woCraftModelAll.AsParallel().WithDegreeOfParallelism(1).ForAll(craft =>
                {
                    var craftWorks = woCrafts.Where(p => p.FWORK_ORDER_ID == craft.FWORK_ORDER_ID)
                                    .OrderBy(p => p.FSHOW_SEQNO)
                                    .Distinct()
                                    .ToList();
                    var schCraft = craftWorks.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID);

                    if (craftWorks.IndexOf(schCraft) > 0)
                    {
                        var preWoCraft = craftWorks[craftWorks.IndexOf(schCraft) - 1];
                        var preFinishQty = craftWorks.Where(p => p.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                                                    .Select(p => p.FFINISH_QTY)
                                                    .FirstOrDefault();

                        //当前工艺已完工+本次完工 大于 上一工艺完工数量
                        if (!preFinishQty.Equals(0))
                        {
                            woCraftModels.Add(craft);
                        }
                    }
                    else if (craftWorks.IndexOf(schCraft) == 0)
                    {
                        woCraftModels.Add(craft);
                    }

                });

                if (woCraftModels.Count > 0)
                {
                    List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                    //取出物料信息
                    DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                    {
                        var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == craft.FMATERIAL_ID);
                        if (material != null)
                        {
                            craft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                            craft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                            craft.FSPEC_DESC = material.FSPEC_DESC;
                            craft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                            craft.FGOODS_MODEL = material.FGOODS_MODEL;
                        }
                        if (jobDatas.Count > 0)
                        {
                            var jobInfos = jobDatas.Where(p => p.FCRAFT_SCHEDULE_ID == craft.FCRAFT_SCHEDULE_ID).ToList();
                            if (jobInfos.Count > 0)
                            {
                                var jobinfo = jobInfos.OrderByDescending(p => p.FCDATE).FirstOrDefault();
                                craft.HandlingJobs = new List<CraftJobBookingModel>() { jobinfo };
                            }

                        }

                    });
                    var saleOrderIds = woCraftModels.Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
                    var saleOrdrpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                    var saleOrderResult = await saleOrdrpcServer.QuerySalesByIdsAsync(saleOrderIds);
                    if (saleOrderResult.StatusCode != 200)
                    {
                        ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                    }
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                    {
                        var saleOrd = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == craft.FSALE_ORDER_ID);
                        if (saleOrd != null)
                        {
                            craft.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                            craft.FPROJECT_ID = saleOrd.FPROJECT_ID;  //项目
                            craft.FPROJECT_NAME = saleOrd.FPROJECT_NAME;
                            craft.FPROJECT_CODE = saleOrd.FPROJECT_CODE;
                        }
                    });

                    //取计划员姓名
                    var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                    if (empIds.Count > 0)
                    {
                        var empResult = await RpcGetEmployeesAsync(empIds);
                        if (empResult.StatusCode == 200)
                        {
                            woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                            {
                                if (!string.IsNullOrWhiteSpace(craft.FPLAN_EMP_ID))
                                {
                                    var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == craft.FPLAN_EMP_ID);
                                    if (emp != null)
                                    {
                                        craft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                    }
                                }
                            });
                        }
                        else
                        {
                            ERROR(empResult, empResult.StatusCode, empResult.Message);
                        }
                    }

                    //取出生产单位
                    var unitIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPRO_UNIT_ID)).Select(p => p.FPRO_UNIT_ID).Distinct().ToList();

                    //重量单位
                    unitIds.AddRange(woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FWEIGHT_UNIT_ID))
                                    .Select(p => p.FWEIGHT_UNIT_ID).Distinct().ToList());

                    if (unitIds.Count > 0)
                    {
                        var unitResult = await RpcGetUnitsAsync(unitIds);
                        if (unitResult.StatusCode == 200)
                        {
                            woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                            {
                                //数量单位
                                if (!string.IsNullOrWhiteSpace(craft.FPRO_UNIT_ID))
                                {
                                    var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FPRO_UNIT_ID);
                                    if (unit != null)
                                    {
                                        craft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                        craft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                    }
                                }

                                //重量单位
                                if (!string.IsNullOrWhiteSpace(craft.FWEIGHT_UNIT_ID))
                                {
                                    var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FWEIGHT_UNIT_ID);
                                    if (unit != null)
                                    {
                                        craft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                    }
                                }
                            });
                        }
                        else
                        {
                            ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                        }
                    }

                    //取出工艺信息
                    var craftIds = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                    if (craftIds.Count > 0)
                    {
                        var craftResult = await RpcGetCraftsAsync(craftIds);
                        if (craftResult.StatusCode != 200)
                        {
                            ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                        }

                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                        {
                            var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                            if (craft != null)
                            {
                                woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                                woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                                woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                            }
                        });
                    }

                    //取出工位
                    var stationIds = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                    if (stationIds.Count > 0)
                    {
                        var stationResult = await RpcGetStationsAsync(stationIds);
                        if (stationResult.StatusCode != 200)
                        {
                            ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                        }
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                        {
                            var craft = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                            if (craft != null)
                            {
                                woCraft.FSTATION_CODE = craft.FSTATION_CODE;
                                woCraft.FSTATION_NAME = craft.FSTATION_NAME;
                            }
                        });
                    }

                    //取出投入物料
                    //await GetSubMaterialAsync(woCraftModels);

                    //取出执行人
                    //await GetScheduleEmpsAsync(woCraftModels);

                    //获取排程的加工状态
                    await GetScheduleStatusAsync(woCraftModels);

                }



            }

            DataResult<object> result = new DataResult<object>
            {
                Entity = woCraftModels.OrderBy(p => p.FMATERIAL_CODE).OrderBy(p => p.SCHEDULE_FSHOW_SEQNO).ToList(),

                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = woCraftModels.Count,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };

            return await OK(result);
        }



        /// <summary>
        /// 查询待加工排程任务
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> QueryWaitJobsAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 100001, string.Format(_multiLang["传入参数 {0} 为空."], "model"));
            }

            //总行数
            RefAsync<int> totalSize = new RefAsync<int>();

            //清除传入字段为空的, MES003报表服务调此接口，传了空字段。MES003暂不能更新给鑫海，先在这里修改
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                model.WhereGroup.Items.RemoveAll(p => string.IsNullOrWhiteSpace(p.FieldName));
            }

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            /*
            string stationId = string.Empty;
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                var stationQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSTATION_ID");
                if (stationQuery != null)
                {
                    stationId = stationQuery.Value;
                }
            }*/

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             //.WhereIF(!string.IsNullOrWhiteSpace(stationId), (sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FSTATION_ID == stationId)             
             .Where((sch, schStatus, wo, woStatus, craft, craftStatus) => (schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2) &&
                                                                           schStatus.FRELEASE_STATUS == true &&
                                                                           sch.FPLAN_QTY > schStatus.FFINISH_QTY)

             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FCDATE, OrderByType.Desc)
             //.OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => schStatus.FACT_ST_DATE, OrderByType.Desc)
             //.OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FPLAN_ST_DATE, OrderByType.Asc)

             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FREMARK = craft.FREMARK,
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 //重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,
                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,

                 SCHEDULE_FSHOW_SEQNO = sch.SCHEDULE_FSHOW_SEQNO

             })
               .Where(wheres)
             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                {
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == craft.FMATERIAL_ID);
                    if (material != null)
                    {
                        craft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        craft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        craft.FSPEC_DESC = material.FSPEC_DESC;
                        craft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                        craft.FGOODS_MODEL = material.FGOODS_MODEL;
                    }
                });

                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                if (empIds.Count > 0)
                {
                    var empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                        {
                            if (!string.IsNullOrWhiteSpace(craft.FPLAN_EMP_ID))
                            {
                                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == craft.FPLAN_EMP_ID);
                                if (emp != null)
                                {
                                    craft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPRO_UNIT_ID)).Select(p => p.FPRO_UNIT_ID).Distinct().ToList();

                //重量单位
                unitIds.AddRange(woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FWEIGHT_UNIT_ID))
                                .Select(p => p.FWEIGHT_UNIT_ID).Distinct().ToList());

                if (unitIds.Count > 0)
                {
                    var unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                        {
                            //数量单位
                            if (!string.IsNullOrWhiteSpace(craft.FPRO_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FPRO_UNIT_ID);
                                if (unit != null)
                                {
                                    craft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                    craft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                }
                            }

                            //重量单位
                            if (!string.IsNullOrWhiteSpace(craft.FWEIGHT_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FWEIGHT_UNIT_ID);
                                if (unit != null)
                                {
                                    craft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                if (craftIds.Count > 0)
                {
                    var craftResult = await RpcGetCraftsAsync(craftIds);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }

                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    });
                }

                //取出工位
                var stationIds = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                if (stationIds.Count > 0)
                {
                    var stationResult = await RpcGetStationsAsync(stationIds);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (craft != null)
                        {
                            woCraft.FSTATION_CODE = craft.FSTATION_CODE;
                            woCraft.FSTATION_NAME = craft.FSTATION_NAME;
                        }
                    });
                }

                //取出投入物料
                await GetSubMaterialAsync(woCraftModels);

                //取出执行人
                await GetScheduleEmpsAsync(woCraftModels);



                //获取排程的加工状态
                await GetScheduleStatusAsync(woCraftModels);
            }

            DataResult<object> result = new DataResult<object>
            {
                Entity = woCraftModels.OrderBy(p => p.SCHEDULE_FSHOW_SEQNO).ToList(),

                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = totalSize,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };

            return await OK(result);
        }

        /// <summary>
        /// 获取排程任务的加工状态
        /// </summary>
        /// <param name="woCraftModels"></param>
        /// <returns></returns>
        private async Task GetScheduleStatusAsync(List<WOCraftScheduleModel> woCraftModels)
        {

            var schIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            if (schIds.Count > 0)
            {
                var rpcServer = this.GetService<IModuleServices.MES007_JobBooking.IMES007JobQueryService>("MES007JobQuery");
                QueryRequestModel model = new QueryRequestModel()
                {
                    PageIndex = 0,
                    PageSize = int.MaxValue,
                    WhereGroup = new QueryWhereGroupModel
                    {
                        GroupType = EnumGroupType.AND,
                        Items = new List<QueryWhereItemModel> {
                            new QueryWhereItemModel{
                                FieldName="booking.FCRAFT_SCHEDULE_ID",
                                OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                                Value=string.Join(",",schIds),
                                }
                            //,new QueryWhereItemModel{
                            //    FieldName="booking.FWORK_STATUS",
                            //    OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.NotEqual,
                            //    Value="cancel",
                            //    }
                        }
                    }
                };
                var bookingResult = await rpcServer.QueryJobBookingAsync(model);
                if (bookingResult.StatusCode != 200)
                {
                    ERROR(bookingResult, bookingResult.StatusCode, bookingResult.Message);
                }

                var bookingData = bookingResult.Entity;
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
                {

                    var bookings = bookingData.Where(j => j.FCRAFT_SCHEDULE_ID == p.FCRAFT_SCHEDULE_ID).ToList();

                    if (bookings.Count > 0)
                    {
                        //完成  加工中    暂停
                        var sumnum = bookings.Sum(s => (s.FPASS_QTY + s.FNG_QTY));//总数

                        var working = bookings.FirstOrDefault(k => k.FWORK_STATUS == "working");//加工中
                        if (working != null)
                        {
                            p.FCRAFT_STATUS = "working";//加工中
                        }

                        var paused = bookings.FirstOrDefault(k => k.FWORK_STATUS == "paused");//暂停中
                        if (paused != null)
                        {
                            p.FCRAFT_STATUS = "paused";//暂停中
                        }
                        var workStatus = bookings.OrderByDescending(p => p.FCDATE).FirstOrDefault();
                        if (workStatus.FWORK_STATUS == "cancel")
                        {
                            p.FCRAFT_STATUS = "cancel";//取消加工
                        }
                        else if (working == null && paused == null)
                        {
                            p.FCRAFT_STATUS = "finished";//已完成
                        }
                    }
                    else
                    {
                        p.FCRAFT_STATUS = "unstart";//未开工
                    }

                });


            }

        }

        /// <summary>
        /// 查询待加工排程任务-------鑫海需要  完工后也可以打印
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> QueryJobsAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 100001, string.Format(_multiLang["传入参数 {0} 为空."], "model"));
            }

            //总行数
            RefAsync<int> totalSize = new RefAsync<int>();

            //清除传入字段为空的, MES003报表服务调此接口，传了空字段。MES003暂不能更新给鑫海，先在这里修改
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                model.WhereGroup.Items.RemoveAll(p => string.IsNullOrWhiteSpace(p.FieldName));
            }

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);

            /*
            string stationId = string.Empty;
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                var stationQuery = model.WhereGroup.Items.FirstOrDefault(p => p.FieldName == "FSTATION_ID");
                if (stationQuery != null)
                {
                    stationId = stationQuery.Value;
                }
            }*/

            //查询
            var woCraftModels = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID))
             //.WhereIF(!string.IsNullOrWhiteSpace(stationId), (sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FSTATION_ID == stationId)             
             //.Where((sch, schStatus, wo, woStatus, craft, craftStatus) => (schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2) &&
             //                                                              schStatus.FRELEASE_STATUS == true &&
             //                                                              sch.FPLAN_QTY > schStatus.FFINISH_QTY)

             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => schStatus.FACT_ST_DATE, OrderByType.Desc)
             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FPLAN_ST_DATE, OrderByType.Asc)

             .Select((sch, schStatus, wo, woStatus, craft, craftStatus) => new WOCraftScheduleModel()
             {
                 FREMARK = craft.FREMARK,
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 //重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,
                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,



             })
               .Where(wheres)
             .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            if (woCraftModels.Count > 0)
            {
                List<string> materialIds1 = woCraftModels.Select(p => p.FMATERIAL_ID).Distinct().ToList();
                //取出物料信息
                DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                if (materialResult.StatusCode != 200)
                {
                    ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                }
                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                {
                    var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == craft.FMATERIAL_ID);
                    if (material != null)
                    {
                        craft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                        craft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                        craft.FSPEC_DESC = material.FSPEC_DESC;
                        craft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                    }
                });

                //取计划员姓名
                var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                if (empIds.Count > 0)
                {
                    var empResult = await RpcGetEmployeesAsync(empIds);
                    if (empResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                        {
                            if (!string.IsNullOrWhiteSpace(craft.FPLAN_EMP_ID))
                            {
                                var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == craft.FPLAN_EMP_ID);
                                if (emp != null)
                                {
                                    craft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(empResult, empResult.StatusCode, empResult.Message);
                    }
                }

                //取出生产单位
                var unitIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPRO_UNIT_ID)).Select(p => p.FPRO_UNIT_ID).Distinct().ToList();

                //重量单位
                unitIds.AddRange(woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FWEIGHT_UNIT_ID))
                                .Select(p => p.FWEIGHT_UNIT_ID).Distinct().ToList());

                if (unitIds.Count > 0)
                {
                    var unitResult = await RpcGetUnitsAsync(unitIds);
                    if (unitResult.StatusCode == 200)
                    {
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                        {
                            //数量单位
                            if (!string.IsNullOrWhiteSpace(craft.FPRO_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FPRO_UNIT_ID);
                                if (unit != null)
                                {
                                    craft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                    craft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                }
                            }

                            //重量单位
                            if (!string.IsNullOrWhiteSpace(craft.FWEIGHT_UNIT_ID))
                            {
                                var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FWEIGHT_UNIT_ID);
                                if (unit != null)
                                {
                                    craft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                }
                            }
                        });
                    }
                    else
                    {
                        ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                    }
                }

                //取出工艺信息
                var craftIds = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                if (craftIds.Count > 0)
                {
                    var craftResult = await RpcGetCraftsAsync(craftIds);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }

                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    });
                }

                //取出工位
                var stationIds = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                if (stationIds.Count > 0)
                {
                    var stationResult = await RpcGetStationsAsync(stationIds);
                    if (stationResult.StatusCode != 200)
                    {
                        ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                    }
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                        if (craft != null)
                        {
                            woCraft.FSTATION_CODE = craft.FSTATION_CODE;
                            woCraft.FSTATION_NAME = craft.FSTATION_NAME;
                        }
                    });
                }

                //取出投入物料
                await GetSubMaterialAsync(woCraftModels);

                //取出执行人
                await GetScheduleEmpsAsync(woCraftModels);
            }

            DataResult<object> result = new DataResult<object>
            {
                Entity = woCraftModels,
                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = totalSize,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };

            return await OK(result);
        }



        /// <summary>
        /// 取出任务的执行人
        /// </summary>
        /// <param name="woCraftModels"></param>
        /// <returns></returns>
        private async Task GetScheduleEmpsAsync(List<WOCraftScheduleModel> woCraftModels)
        {
            var db = _isugar.DB;

            var schIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).ToList();

            List<WOCraftSchedulePersonModel> persons = await db.Queryable<T_MESD_CRAFT_SCHEDULE_PERSON>()
                                                    .Where(p => schIds.Contains(p.FCRAFT_SCHEDULE_ID))
                                                    .Select(p => new WOCraftSchedulePersonModel
                                                    {
                                                        FCRAFT_SCHEDULE_ID = p.FCRAFT_SCHEDULE_ID,
                                                        FCRAFT_SCHEDULE_PERSON_ID = p.FCRAFT_SCHEDULE_PERSON_ID,
                                                        FECODE = p.FECODE,
                                                        FEMP_ID = p.FEMP_ID,
                                                        FSHOW_SEQNO = p.FSHOW_SEQNO,
                                                    })
                                                    .ToListAsync();

            if (persons.Count > 0)
            {
                var empIds = persons.Select(p => p.FEMP_ID).Distinct().ToList();
                var empsResult = await RpcGetEmployeesAsync(empIds);
                if (empsResult.StatusCode != 200)
                {
                    ERROR(empsResult, empsResult.StatusCode, empsResult.Message);
                }

                persons.AsParallel().WithDegreeOfParallelism(4).ForAll(person =>
                {
                    var emp = empsResult.Entity.FirstOrDefault(p => p.FEMP_ID == person.FEMP_ID);
                    if (emp != null)
                    {
                        person.FEMP_CODE = emp.FEMP_CODE;
                        person.FEMP_NAME = emp.FEMP_NAME;
                    }

                });

                woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                {
                    craft.Persons = persons.Where(p => p.FCRAFT_SCHEDULE_ID == craft.FCRAFT_SCHEDULE_ID).OrderBy(p => p.FSHOW_SEQNO).ToList();
                    if (craft.Persons.Count > 0)
                    {
                        craft.FEMP = string.Join(",", craft.Persons.Select(p => p.FEMP_NAME).ToList());
                    }
                });
            }
        }


        /// <summary>
        /// 取出排程任务的投入材料
        /// </summary>
        /// <returns></returns>
        private async Task GetSubMaterialAsync(List<WOCraftScheduleModel> woCraftModels)
        {
            var db = _isugar.DB;

            //取出投入物料
            var woIds = woCraftModels.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
            if (woIds.Count > 0)
            {
                var subWOMaterials = await db.Queryable<T_MESD_WORK_ORDER_MATERIAL>().Where(p => p.FWORK_ORDER_CRAFT_ID != string.Empty && woIds.Contains(p.FWORK_ORDER_ID))
                    .Select(p => new { p.FWORK_ORDER_ID, p.FWORK_ORDER_CRAFT_ID, p.FSUB_MATERIAL_ID })
                    .ToListAsync();
                if (subWOMaterials.Count > 0)
                {
                    var subMaterialIds = subWOMaterials.Select(p => p.FSUB_MATERIAL_ID).Distinct().ToList();
                    var materialsResult = await RpcGetMaterialsAsync(subMaterialIds);
                    if (materialsResult.StatusCode == 200)
                    {
                        var daoMaterials = materialsResult.Entity;
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                        {
                            var subMaterils = (from subWOMaterial in subWOMaterials.Where(p => p.FWORK_ORDER_ID == woCraft.FWORK_ORDER_ID
                                                                                            && p.FWORK_ORDER_CRAFT_ID == woCraft.FWORK_ORDER_CRAFT_ID)
                                               join daoMaterial in daoMaterials
                                               on subWOMaterial.FSUB_MATERIAL_ID equals daoMaterial.FMATERIAL_ID
                                               select string.Concat(daoMaterial.FMATERIAL_CODE, "/", daoMaterial.FMATERIAL_NAME)).ToList();

                            woCraft.FSUB_MATERIAL = string.Join(",", subMaterils);
                        });
                    }
                    else
                    {
                        ERROR(materialsResult, materialsResult.StatusCode, materialsResult.Message);
                    }
                }
            }
        }

        /// <summary>
        /// 返回物料信息
        /// </summary>
        /// <param name="materialIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleMaterialModel>>> RpcGetMaterialsAsync(List<string> materialIds)
        {
            var rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");
            return await rpcServer.GetForBusinessByIdsAsync(materialIds);
        }


        /// <summary>
        /// 返回员工姓名
        /// </summary>
        /// <param name="empIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<EmployeeModel>>> RpcGetEmployeesAsync(List<string> empIds)
        {
            var rpcServer = this.GetService<IEmployeeService>("ADM024Employee");
            return await rpcServer.GetEmpsByIdsAsync(empIds);
        }

        /// <summary>
        /// 返回工艺信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleCraftModel>>> RpcGetCraftsAsync(List<string> craftIds)
        {
            var rpcServer = this.GetService<IMES002CraftService>("MES002Craft");
            return await rpcServer.GetForBusinessByIdsAsync(craftIds);
        }


        /// <summary>
        /// 返回工艺信息
        /// </summary>
        /// <param name="craftIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleStationModel>>> RpcGetStationsAsync(List<string> stationIds)
        {
            var rpcServer = this.GetService<IMES001StationService>("MES001Station");
            return await rpcServer.GetForBusinessByIdsAsync(stationIds);
        }

        /// <summary>
        /// 返回单位信息
        /// </summary>
        /// <param name="unitIds"></param>
        /// <returns></returns>
        private async Task<DataResult<List<SimpleUnitModel>>> RpcGetUnitsAsync(List<string> unitIds)
        {
            var rpcServer = this.GetService<IMSD001UnitService>("MSD001Unit");
            return await rpcServer.GetByIdsForBusinessAsync(unitIds);
        }


        /// <summary>
        /// 返回工艺id列表
        /// </summary>
        /// <param name="craft"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetCraftIdsAsync(string craft)
        {
            IMES002CraftService rpcServer = this.GetService<IMES002CraftService>("MES002Craft");

            QueryRequestModel model = new QueryRequestModel();

            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FCRAFT_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = craft,
            };
            model.WhereGroup.Items.Add(whereItem);

            whereItem = new QueryWhereItemModel()
            {
                FieldName = "FCRAFT_NAME",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = craft,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.QueryCraftIdsAsync(model);
        }

        /// <summary>
        /// 返回物料id列表
        /// </summary>
        /// <param name="material"></param>
        /// <returns></returns>
        private async Task<DataResult<List<string>>> RpcGetMaterialIdsAsync(string material)
        {
            IMSD002MaterialService rpcServer = this.GetService<IMSD002MaterialService>("MSD002Material");

            QueryRequestModel model = new QueryRequestModel();
            model.WhereGroup = new QueryWhereGroupModel();
            model.WhereGroup.GroupType = EnumGroupType.OR;
            model.WhereGroup.Items = new List<QueryWhereItemModel>();

            QueryWhereItemModel whereItem = new QueryWhereItemModel()
            {
                FieldName = "FMATERIAL_CODE",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = material,
            };
            model.WhereGroup.Items.Add(whereItem);

            whereItem = new QueryWhereItemModel()
            {
                FieldName = "FMATERIAL_NAME",
                OperatorType = Core.HCPlatform.Utilities.EnumQuerySymbol.Like,
                Value = material,
            };
            model.WhereGroup.Items.Add(whereItem);

            return await rpcServer.GetMaterialIDSAsync(model);
        }
        #endregion

        /// <summary>
        /// 验证工单是否在加工中
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        public async Task<DataResult<bool>> ValidateWorkOrderJobBookingAsync(List<string> woIds)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            DataResult<bool> result = new DataResult<bool>() { };

            var job = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>
                ((job, sch, wo) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                       JoinType.Left, job.FWORK_ORDER_ID == wo.FWORK_ORDER_ID))
                .Where((job, sch, wo) => woIds.Contains(job.FWORK_ORDER_ID) &&
                        (job.FWORK_STATUS == WorkStatus.working.ToString() || job.FWORK_STATUS == WorkStatus.paused.ToString()))
                .Select((job, sch, wo) => new { job.FCRAFT_JOB_BOOKING_NO, sch.FCRAFT_SCHEDULE_NO, wo.FWORK_ORDER_NO, job.FWORK_STATUS })
                .FirstAsync();

            if (job != null)
            {
                result.Entity = false;
                result.StatusCode = 109001;
                result.Message = string.Format(_multiLang["执行失败, 工单编号 {0}, 排程任务编号 {1}, 加工任务编号 {2}, 状态为{3}"],
                      job.FWORK_ORDER_NO, job.FCRAFT_SCHEDULE_NO, job.FCRAFT_JOB_BOOKING_NO,
                      _multiLang[(job.FWORK_STATUS == WorkStatus.working.ToString() ? "加工中" : "已暂停")]);
                return await OK(result);
            }

            result.Entity = true;
            result.StatusCode = 200;
            return await OK(result);

        }

        /// <summary>
        /// 验证排程编号是否在加工中
        /// </summary>
        /// <param name="woIds"></param>
        /// <returns></returns>
        public async Task<DataResult<bool>> ValidateScheduleJobBookingAsync(List<string> schIds)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            DataResult<bool> result = new DataResult<bool>() { };

            var job = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_WORK_ORDER>
                ((job, sch, wo) => new JoinQueryInfos(JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                       JoinType.Left, job.FWORK_ORDER_ID == wo.FWORK_ORDER_ID))
                .Where((job, sch, wo) => schIds.Contains(job.FCRAFT_SCHEDULE_ID) &&
                        (job.FWORK_STATUS == WorkStatus.working.ToString() || job.FWORK_STATUS == WorkStatus.paused.ToString()))
                .Select((job, sch, wo) => new { job.FCRAFT_JOB_BOOKING_NO, sch.FCRAFT_SCHEDULE_NO, wo.FWORK_ORDER_NO, job.FWORK_STATUS })
                .FirstAsync();

            if (job != null)
            {
                result.Entity = false;
                result.StatusCode = 109001;
                result.Message = string.Format(_multiLang["执行失败, 工单编号 {0}, 排程任务编号 {1}, 加工任务编号 {2}, 状态为{3}"],
                      job.FWORK_ORDER_NO, job.FCRAFT_SCHEDULE_NO, job.FCRAFT_JOB_BOOKING_NO,
                      _multiLang[(job.FWORK_STATUS == WorkStatus.working.ToString() ? "加工中" : "已暂停")]);
                return await OK(result);
            }

            result.Entity = true;
            result.StatusCode = 200;
            return await OK(result);

        }
        /// <summary>
        /// 根据排程任务id查询工艺报工
        /// </summary>
        public async Task<DataResult<List<CraftJobBookingModel>>> GetJobBookingByAsync(string schId)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>() { };

            var job = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => schId == p.FCRAFT_SCHEDULE_ID).Select<CraftJobBookingModel>().ToListAsync();


            result.Entity = job;
            result.StatusCode = 200;
            return await OK(result);
        }

        /// <summary>
        /// 根据排程任务id查询工艺报工
        /// </summary>
        public async Task<DataResult<List<CraftJobBookingModel>>> GetJobBookingByAsync(List<string> Ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            DataResult<List<CraftJobBookingModel>> result = new DataResult<List<CraftJobBookingModel>>() { };

            var job = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => Ids.Contains(p.FCRAFT_SCHEDULE_ID)).Select<CraftJobBookingModel>().ToListAsync();


            result.Entity = job;
            result.StatusCode = 200;
            return await OK(result);
        }

        /// <summary>
        /// 按id返回加工任务
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private async Task<CraftJobBookingModel> GetJobBookingByIdAsync(string id)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //取出进行中的加工任务
            CraftJobBookingModel bookingModel = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                    ((job, sch, schStatus) => new JoinQueryInfos(
                        JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((job, sch, schStatus) => job.FCRAFT_JOB_BOOKING_ID == id)
                .Select((job, sch, schStatus) => new CraftJobBookingModel
                {
                    FACT_ED_DATE = job.FACT_ED_DATE,
                    FACT_ST_DATE = job.FACT_ST_DATE,

                    FACT_USE_HOUR = job.FACT_USE_HOUR,

                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                    FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                    FEMP_ID = job.FEMP_ID,

                    FEMP_NAME = job.FEMP_NAME,
                    FLAST_PAUSE_DATE = job.FLAST_PAUSE_DATE,

                    FLAST_RESUME_DATE = job.FLAST_RESUME_DATE,
                    FNG_QTY = job.FNG_QTY,

                    FPASS_QTY = job.FPASS_QTY,
                    FWORK_STATUS = job.FWORK_STATUS,

                    FCRAFT_ID = job.FCRAFT_ID,
                    FMATERIAL_ID = job.FMATERIAL_ID,

                    FSALE_ORDER_ID = job.FSALE_ORDER_ID,
                    FSTATION_ID = job.FSTATION_ID,

                    FWORK_ORDER_CRAFT_ID = job.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_ID = job.FWORK_ORDER_ID,

                    FNG_WEIGHT = job.FNG_WEIGHT,
                    FPASS_WEIGHT = job.FPASS_WEIGHT,

                    FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,
                    FPLAN_QTY_SCHEDULE = sch.FPLAN_QTY,

                    FPLAN_WEIGHT_SCHEDULE = sch.FPLAN_WEIGHT,
                    FFINISH_WEIGHT_SCHEDULE = schStatus.FFINISH_WEIGHT

                })
                .FirstAsync();

            return bookingModel;
        }


        /// <summary>
        ///  验证取消完工
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<JobBookingOperateModel>> ValidateCancelCompletionAsync(List<string> ids)
        {
            var result = new DataResult<JobBookingOperateModel>();
            result.StatusCode = 200;
            var db = _isugar.DB;

            // 1. 获取要取消的报工记录
            var jobBookings = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => ids.Contains(p.FCRAFT_JOB_BOOKING_ID))
                .ToListAsync();

            // 2. 检查报工自身状态是否为"已完工"
            var notFinishedJobs = jobBookings.Where(p => p.FWORK_STATUS != "finished").ToList();
            if (notFinishedJobs.Any())
            {
                result.Message = $"加工任务 {notFinishedJobs[0].FCRAFT_JOB_BOOKING_NO} 尚未完工，无法执行此操作";
                result.StatusCode = 1010;
                return await OK(result);
            }

            // ========================= 检查下游任务状态 =========================
            // 3. 查找由这些报工记录创建的所有下游子排程
            var childScheduleIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(s => ids.Contains(s.FSOURCE_JOB_BOOKING_ID))
                .Select(s => s.FCRAFT_SCHEDULE_ID)
                .ToListAsync();

            if (childScheduleIds.Any())
            {
                var activeDownstreamJobData = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                    .InnerJoin<T_MESD_CRAFT_SCHEDULE>((job, sch) => job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID)
                    .Where((job, sch) =>
                    childScheduleIds.Contains(job.FCRAFT_SCHEDULE_ID) &&
                    (
                        job.FWORK_STATUS == "working" ||
                        job.FWORK_STATUS == "paused" ||
                        job.FWORK_STATUS == "finished" ||
                        job.FWORK_STATUS == "partfinished"
                    ))
                    .Select((job, sch) => new
                    {
                        SourceJobBookingId = sch.FSOURCE_JOB_BOOKING_ID,
                        DownstreamJobNo = job.FCRAFT_JOB_BOOKING_NO,
                        DownstreamScheduleNo = sch.FCRAFT_SCHEDULE_NO
                    })
                    .FirstAsync();

                // 5. 如果查到了下游任务，再在C#内存中组装错误信息
                if (activeDownstreamJobData != null)
                {
                    // 从已查询的 jobBookings 列表中安全地查找上游报工单号
                    var upstreamJob = jobBookings.FirstOrDefault(jb => jb.FCRAFT_JOB_BOOKING_ID == activeDownstreamJobData.SourceJobBookingId);
                    string upstreamJobNo = upstreamJob?.FCRAFT_JOB_BOOKING_NO ?? "未知";

                    result.Message = $"无法取消报工 [{upstreamJobNo}]：其创建的下游排程任务 [{activeDownstreamJobData.DownstreamScheduleNo}] 已开工（加工任务号: {activeDownstreamJobData.DownstreamJobNo}）。";
                    result.StatusCode = 1010;
                    return await OK(result);
                }
            }
            // ==================================================================

            // 5. 检查关联的工单入库单是否已审核
            var workInDatas = await db.Queryable<T_MESD_WORK_IN_PRODUCT, T_MESD_WORK_IN, T_MESD_WORK_IN_STATUS>(
                (a, b, c) => new JoinQueryInfos(
                    JoinType.Left, a.FWORK_IN_ID == b.FWORK_IN_ID,
                    JoinType.Left, b.FWORK_IN_ID == c.FWORK_IN_ID))
                .Where((a, b, c) => ids.Contains(a.FCRAFT_JOB_BOOKING_ID) && c.FCFLAG == 1)
                .Select((a, b, c) => new { a.FCRAFT_JOB_BOOKING_NO, b.FWORK_IN_NO })
                .ToListAsync();

            if (workInDatas.Any())
            {
                result.Message = $"加工任务单 {workInDatas[0].FCRAFT_JOB_BOOKING_NO} 对应的工单入库单 {workInDatas[0].FWORK_IN_NO} 已审核，无法取消。";
                result.StatusCode = 1010;
                return await OK(result);
            }

            // 6. 检查工单是否已结案
            var workOrdIds = jobBookings.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
            var closedWorkOrders = await db.Queryable<T_MESD_WORK_ORDER_STATUS>()
                .Where(p => workOrdIds.Contains(p.FWORK_ORDER_ID) && (p.FIF_CLOSE == 1 || p.FIF_CLOSE == 3))
                .ToListAsync();

            if (closedWorkOrders.Any())
            {
                var workOrdCraftInfos = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => workOrdIds.Contains(p.FWORK_ORDER_ID)).ToListAsync();
                foreach (var woStatus in closedWorkOrders)
                {
                    var lastCraft = workOrdCraftInfos.Where(p => p.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID).OrderBy(p => p.FSHOW_SEQNO).LastOrDefault();
                    var jobsInThisWo = jobBookings.Where(j => j.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID);

                    // 如果工单已结案，只有属于最后一道工序的报工才可能被取消
                    if (jobsInThisWo.Any(j => j.FWORK_ORDER_CRAFT_ID != lastCraft?.FWORK_ORDER_CRAFT_ID))
                    {
                        var problemJob = jobsInThisWo.First(j => j.FWORK_ORDER_CRAFT_ID != lastCraft?.FWORK_ORDER_CRAFT_ID);
                        result.Message = $"工单已结案，只有最后一道工序允许操作。加工任务 [{problemJob.FCRAFT_JOB_BOOKING_NO}] 不属于最后一道工序，无法取消。";
                        result.StatusCode = 1010;
                        return await OK(result);
                    }
                }
            }

            return await OK(result);
        }


        /// <summary>
        /// 取消完工
        /// </summary>
        /// <param name="ids">要取消的工艺报工ID (FCRAFT_JOB_BOOKING_ID) 列表</param>
        /// <returns>返回新创建的"加工中"任务ID列表</returns>
        public async Task<DataResult<List<string>>> CancelCompletion(List<string> ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();
            var dataResult = new DataResult<List<string>>();
            var currentTime = _iauth.GetCurDateTime();

            // 步骤 1: 执行取消完工的合法性验证
            var validate = await ValidateCancelCompletionAsync(ids);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 102001, _multiLang["取消完工验证失败.错误信息:" + validate.Message]);
            }

            // ===================================================================================
            // 步骤 2: 在事务外提前查询所有需要的数据
            // ===================================================================================
            var jobBookingsToCancel = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(p => ids.Contains(p.FCRAFT_JOB_BOOKING_ID))
                .ToListAsync();

            var oldJobData = JsonConvert.DeserializeObject<List<T_MESD_CRAFT_JOB_BOOKING>>(JsonConvert.SerializeObject(jobBookingsToCancel));

            var workOrderCraftIds = jobBookingsToCancel.Select(j => j.FWORK_ORDER_CRAFT_ID).Distinct().ToList();
            var scheduleIds = jobBookingsToCancel.Select(j => j.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            var workOrderIds = jobBookingsToCancel.Select(j => j.FWORK_ORDER_ID).Distinct().ToList();

            // 调试信息：记录要取消的报工ID
            var idsDebugInfo = string.Join(", ", ids);
            System.Diagnostics.Debug.WriteLine($"要取消的报工ID列表: {idsDebugInfo}");

            // 查找由这些报工创建的所有拆分排程（包括子排程和同级拆分排程）
            var allSplitSchedulesToDelete = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(s => ids.Contains(s.FSOURCE_JOB_BOOKING_ID))
                .ToListAsync();

            // 调试信息：记录查找到的拆分排程
            System.Diagnostics.Debug.WriteLine($"查询到的拆分排程数量: {allSplitSchedulesToDelete.Count}");
            if (allSplitSchedulesToDelete.Any())
            {
                var debugInfo = string.Join(", ", allSplitSchedulesToDelete.Select(s =>
                    $"{s.FCRAFT_SCHEDULE_NO}(ID:{s.FCRAFT_SCHEDULE_ID}, Parent:{s.FPARENT_CRAFT_SCHEDULE_ID}, Source:{s.FSOURCE_JOB_BOOKING_ID})"));
                System.Diagnostics.Debug.WriteLine($"找到需要删除的拆分排程: {debugInfo}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("没有找到任何拆分排程");
            }

            // 分类处理：拆分排程、下游子排程、复用排程
            var splitSchedulesToDelete = new List<T_MESD_CRAFT_SCHEDULE>(); // 拆分排程（同工艺）
            var childSchedulesToDelete = new List<T_MESD_CRAFT_SCHEDULE>(); // 下游子排程（不同工艺）
            var reusedParentSchedulesToReset = allSplitSchedulesToDelete.Where(s => s.FPARENT_CRAFT_SCHEDULE_ID == null).ToList();

            // 查询所有父排程信息来区分拆分排程和下游子排程
            var parentScheduleIds = allSplitSchedulesToDelete.Where(s => s.FPARENT_CRAFT_SCHEDULE_ID != null)
                .Select(s => s.FPARENT_CRAFT_SCHEDULE_ID).Distinct().ToList();

            var parentSchedules = new Dictionary<string, T_MESD_CRAFT_SCHEDULE>();
            if (parentScheduleIds.Any())
            {
                var parentScheduleList = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().In(parentScheduleIds).ToListAsync();
                parentSchedules = parentScheduleList.ToDictionary(s => s.FCRAFT_SCHEDULE_ID);
            }

            foreach (var schedule in allSplitSchedulesToDelete.Where(s => s.FPARENT_CRAFT_SCHEDULE_ID != null))
            {
                if (parentSchedules.TryGetValue(schedule.FPARENT_CRAFT_SCHEDULE_ID, out var parentSchedule))
                {
                    // 通过工艺ID判断是拆分排程还是下游子排程
                    if (schedule.FWORK_ORDER_CRAFT_ID == parentSchedule.FWORK_ORDER_CRAFT_ID)
                    {
                        // 同工艺：拆分排程
                        splitSchedulesToDelete.Add(schedule);
                        System.Diagnostics.Debug.WriteLine($"拆分排程: {schedule.FCRAFT_SCHEDULE_NO} -> 父排程: {parentSchedule.FCRAFT_SCHEDULE_NO}");
                    }
                    else
                    {
                        // 不同工艺：下游子排程
                        childSchedulesToDelete.Add(schedule);
                        System.Diagnostics.Debug.WriteLine($"下游子排程: {schedule.FCRAFT_SCHEDULE_NO} -> 父排程: {parentSchedule.FCRAFT_SCHEDULE_NO}");
                    }
                }
                else
                {
                    // 无法找到父排程，默认当作下游子排程处理
                    childSchedulesToDelete.Add(schedule);
                    System.Diagnostics.Debug.WriteLine($"无法找到父排程的排程: {schedule.FCRAFT_SCHEDULE_NO} (父ID: {schedule.FPARENT_CRAFT_SCHEDULE_ID})");
                }
            }

            var parentSchedulesToUpdate = new List<T_MESD_CRAFT_SCHEDULE>();
            var originalSchedulesToUpdate = new List<T_MESD_CRAFT_SCHEDULE>();

            // 处理下游子排程的数量归还
            if (childSchedulesToDelete.Any())
            {
                var downstreamCraftIds = childSchedulesToDelete.Select(cs => cs.FWORK_ORDER_CRAFT_ID).Distinct();
                workOrderCraftIds.AddRange(downstreamCraftIds);
                workOrderCraftIds = workOrderCraftIds.Distinct().ToList();

                var parentScheduleIds = childSchedulesToDelete.Select(cs => cs.FPARENT_CRAFT_SCHEDULE_ID).Distinct().ToList();
                if (parentScheduleIds.Any())
                {
                    var parentSchedules = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().In(parentScheduleIds).ToListAsync();
                    var qtyToRestoreGrouped = childSchedulesToDelete.GroupBy(cs => cs.FPARENT_CRAFT_SCHEDULE_ID).Select(g => new { ParentId = g.Key, QtyToRestore = g.Sum(cs => cs.FPLAN_QTY) });

                    foreach (var restoreInfo in qtyToRestoreGrouped)
                    {
                        var parent = parentSchedules.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == restoreInfo.ParentId);
                        if (parent != null)
                        {
                            if (parent.FPLAN_QTY + restoreInfo.QtyToRestore > parent.FORI_PLAN_QTY)
                            {
                                ERROR(null, 102002, $"无法取消完工：归还数量会导致下游排程任务[{parent.FCRAFT_SCHEDULE_NO}]的计划数({parent.FPLAN_QTY + restoreInfo.QtyToRestore})超出其原始计划数({parent.FORI_PLAN_QTY})。请检查是否存在并行操作或手动修改。");
                            }
                            parent.FPLAN_QTY += restoreInfo.QtyToRestore;
                            parentSchedulesToUpdate.Add(parent);
                        }
                    }
                }
            }

            // 处理拆分排程的数量合并回原始排程
            if (splitSchedulesToDelete.Any())
            {
                // 查找这些拆分排程的父排程（原始排程）
                var parentScheduleIds = splitSchedulesToDelete.Select(s => s.FPARENT_CRAFT_SCHEDULE_ID).Distinct().ToList();
                var parentSchedules = await db.Queryable<T_MESD_CRAFT_SCHEDULE>().In(parentScheduleIds).ToListAsync();

                var qtyToMergeGrouped = splitSchedulesToDelete.GroupBy(s => s.FPARENT_CRAFT_SCHEDULE_ID)
                    .Select(g => new { ParentId = g.Key, QtyToMerge = g.Sum(s => s.FPLAN_QTY) });

                foreach (var mergeInfo in qtyToMergeGrouped)
                {
                    var parentSchedule = parentSchedules.FirstOrDefault(p => p.FCRAFT_SCHEDULE_ID == mergeInfo.ParentId);
                    if (parentSchedule != null)
                    {
                        // 将拆分排程的数量合并回父排程（原始排程）
                        parentSchedule.FPLAN_QTY += mergeInfo.QtyToMerge;
                        originalSchedulesToUpdate.Add(parentSchedule);
                    }
                }
            }

            var allJobsForAffectedSchedules = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>()
                .Where(j => scheduleIds.Contains(j.FCRAFT_SCHEDULE_ID))
                .ToListAsync();

            var scheduleStatuses = await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>().Where(s => scheduleIds.Contains(s.FCRAFT_SCHEDULE_ID)).ToListAsync();
            var workCraftStatuses = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>().Where(wc => workOrderCraftIds.Contains(wc.FWORK_ORDER_CRAFT_ID)).ToListAsync();
            var workOrderStatuses = await db.Queryable<T_MESD_WORK_ORDER_STATUS>().Where(wo => workOrderIds.Contains(wo.FWORK_ORDER_ID)).ToListAsync();
            var allWoCrafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(c => workOrderIds.Contains(c.FWORK_ORDER_ID)).OrderBy(c => c.FSHOW_SEQNO).ToListAsync();
            var workInProducts = await db.Queryable<T_MESD_WORK_IN_PRODUCT>().Where(p => ids.Contains(p.FCRAFT_JOB_BOOKING_ID)).ToListAsync();

            var allWorkOrderCraftIdsForReleaseQty = workOrderCraftIds.Union(childSchedulesToDelete.Select(cs => cs.FWORK_ORDER_CRAFT_ID)).Distinct().ToList();
            var allSchedulesForReleaseQty = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .InnerJoin<T_MESD_CRAFT_SCHEDULE_STATUS>((sch, schStatus) => sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID)
                .Where((sch, schStatus) => allWorkOrderCraftIdsForReleaseQty.Contains(sch.FWORK_ORDER_CRAFT_ID) && schStatus.FRELEASE_STATUS == true)
                .Select((sch, schStatus) => new { sch.FCRAFT_SCHEDULE_ID, sch.FWORK_ORDER_CRAFT_ID, sch.FPLAN_QTY })
                .ToListAsync();

            // ===================================================================================
            // 步骤 3: 在内存中准备所有需要更新、插入、删除的数据
            // ===================================================================================

            // 准备 ScheduleStatus 更新
            foreach (var status in scheduleStatuses)
            {
                var validJobs = allJobsForAffectedSchedules.Where(j => j.FCRAFT_SCHEDULE_ID == status.FCRAFT_SCHEDULE_ID && !ids.Contains(j.FCRAFT_JOB_BOOKING_ID));
                status.FPASS_QTY = validJobs.Sum(j => j.FPASS_QTY);
                status.FNG_QTY = validJobs.Sum(j => j.FNG_QTY);
                status.FFINISH_QTY = status.FPASS_QTY + status.FNG_QTY;
                status.FPASS_WEIGHT = validJobs.Sum(j => j.FPASS_WEIGHT);
                status.FNG_WEIGHT = validJobs.Sum(j => j.FNG_WEIGHT);
                status.FFINISH_WEIGHT = status.FPASS_WEIGHT + status.FNG_WEIGHT;
                status.FACT_USE_HOUR = validJobs.Sum(j => j.FACT_USE_HOUR);
                status.FACT_ED_DATE = validJobs.Any() ? validJobs.Max(j => j.FACT_ED_DATE) : null;
                if (status.FIF_CLOSE == 1 || status.FIF_CLOSE == 3)
                {
                    status.FIF_CLOSE = 2;
                    status.FCLOSEDATE = currentTime;
                    status.FCLOSER_ID = user.UserPsnId;
                    status.FCLOSER = user.UserPsnName;
                }
            }

            // 准备 WorkCraftStatus 更新
            var childIdsToDeleteSet = childSchedulesToDelete.Select(s => s.FCRAFT_SCHEDULE_ID).ToHashSet();
            var releasedSchedulesData = allSchedulesForReleaseQty.Where(s => !childIdsToDeleteSet.Contains(s.FCRAFT_SCHEDULE_ID)).ToList();
            var craftReleaseQtys = releasedSchedulesData.GroupBy(item => item.FWORK_ORDER_CRAFT_ID).ToDictionary(g => g.Key, g => g.Sum(item => item.FPLAN_QTY));

            var allSchedulesForWOs = await db.Queryable<T_MESD_CRAFT_SCHEDULE>()
                .Where(s => workOrderIds.Contains(s.FWORK_ORDER_ID)).Select(s => new { s.FCRAFT_SCHEDULE_ID, s.FWORK_ORDER_CRAFT_ID }).ToListAsync();

            foreach (var wcStatus in workCraftStatuses)
            {
                var relevantScheduleIds = allSchedulesForWOs.Where(s => s.FWORK_ORDER_CRAFT_ID == wcStatus.FWORK_ORDER_CRAFT_ID).Select(s => s.FCRAFT_SCHEDULE_ID);
                wcStatus.FFINISH_QTY = scheduleStatuses.Where(ss => relevantScheduleIds.Contains(ss.FCRAFT_SCHEDULE_ID)).Sum(ss => ss.FFINISH_QTY);
                wcStatus.FRELEASE_QTY = craftReleaseQtys.GetValueOrDefault(wcStatus.FWORK_ORDER_CRAFT_ID, 0);
            }

            // 准备 WorkOrderStatus 更新
            foreach (var woStatus in workOrderStatuses)
            {
                var lastCraft = allWoCrafts.LastOrDefault(c => c.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID);
                if (lastCraft != null)
                {
                    var lastCraftStatus = workCraftStatuses.FirstOrDefault(wcs => wcs.FWORK_ORDER_CRAFT_ID == lastCraft.FWORK_ORDER_CRAFT_ID);
                    if (lastCraftStatus != null) woStatus.FFINISH_QTY = lastCraftStatus.FFINISH_QTY;
                }
                if (woStatus.FIF_CLOSE == 1 || woStatus.FIF_CLOSE == 3)
                {
                    woStatus.FIF_CLOSE = 2;
                    woStatus.FCLOSEDATE = currentTime;
                    woStatus.FCLOSER_ID = user.UserPsnId;
                    woStatus.FCLOSER = user.UserPsnName;
                }
            }

            // 准备 JobBooking 更新
            foreach (var job in jobBookingsToCancel)
            {
                job.FWORK_STATUS = "cancel";
                job.FPASS_QTY = 0;
                job.FNG_QTY = 0;
                job.FPASS_WEIGHT = 0;
                job.FNG_WEIGHT = 0;
                job.FACT_USE_HOUR = 0;
            }

            var workInIds = workInProducts.Select(p => p.FWORK_IN_ID).Distinct().ToList();

            // 准备新 JobBooking 插入
            var newWorkingJobs = new List<T_MESD_CRAFT_JOB_BOOKING>();
            var newBillCodes = await GetBillCodesAsync(jobBookingsToCancel.Count);
            if (newBillCodes.Count != jobBookingsToCancel.Count)
            {
                throw new Exception("生成新的加工任务编号失败。");
            }

            for (int i = 0; i < jobBookingsToCancel.Count; i++)
            {
                var canceledJob = jobBookingsToCancel[i];
                var newWorkingJob = new T_MESD_CRAFT_JOB_BOOKING
                {
                    FCRAFT_JOB_BOOKING_ID = GuidHelper.NewGuid(),
                    FCRAFT_JOB_BOOKING_NO = newBillCodes[i],
                    FCRAFT_SCHEDULE_ID = canceledJob.FCRAFT_SCHEDULE_ID,
                    FACT_ST_DATE = currentTime,
                    FWORK_STATUS = WorkStatus.unstart.ToString(),
                    FEMP_ID = canceledJob.FEMP_ID,
                    FEMP_NAME = canceledJob.FEMP_NAME,
                    FSTATION_ID = canceledJob.FSTATION_ID,
                    FCRAFT_ID = canceledJob.FCRAFT_ID,
                    FSALE_ORDER_ID = canceledJob.FSALE_ORDER_ID,
                    FWORK_ORDER_ID = canceledJob.FWORK_ORDER_ID,
                    FMATERIAL_ID = canceledJob.FMATERIAL_ID,
                    FWORK_ORDER_CRAFT_ID = canceledJob.FWORK_ORDER_CRAFT_ID,
                };
                newWorkingJobs.Add(newWorkingJob);
            }

            // 准备 UpdateScheduleStartDate 的更新
            if (newWorkingJobs.Any())
            {
                var schedulesWithNewJobs = newWorkingJobs.GroupBy(j => j.FCRAFT_SCHEDULE_ID);
                foreach (var group in schedulesWithNewJobs)
                {
                    var scheduleId = group.Key;
                    var schStatusToUpdate = scheduleStatuses.FirstOrDefault(s => s.FCRAFT_SCHEDULE_ID == scheduleId);
                    // 只有在从未开工过的情况下，才更新首次开工时间
                    if (schStatusToUpdate != null && !schStatusToUpdate.FACT_ST_DATE.HasValue)
                    {
                        schStatusToUpdate.FACT_ST_DATE = group.Min(j => j.FACT_ST_DATE);
                    }
                }
            }

            // ===================================================================================
            // 步骤 4: 执行数据库事务 
            // ===================================================================================
            db.BeginTran();
            try
            {
                // 删除所有拆分排程（包括子排程和同级拆分排程）
                if (allSplitSchedulesToDelete.Any())
                {
                    var allSplitIds = allSplitSchedulesToDelete.Select(s => s.FCRAFT_SCHEDULE_ID).ToList();
                    await db.Deleteable<T_MESD_CRAFT_SCHEDULE_PERSON>().Where(p => allSplitIds.Contains(p.FCRAFT_SCHEDULE_ID)).ExecuteCommandAsync();
                    await db.Deleteable<T_MESD_CRAFT_SCHEDULE_STATUS>().Where(p => allSplitIds.Contains(p.FCRAFT_SCHEDULE_ID)).ExecuteCommandAsync();
                    await db.Deleteable<T_MESD_CRAFT_SCHEDULE>().Where(p => allSplitIds.Contains(p.FCRAFT_SCHEDULE_ID)).ExecuteCommandAsync();
                }

                // 更新父排程数量（下游任务的父排程）
                if (parentSchedulesToUpdate.Any())
                {
                    await db.Updateable(parentSchedulesToUpdate).UpdateColumns(it => new { it.FPLAN_QTY }).ExecuteCommandAsync();
                }

                // 更新原始排程数量（拆分排程的原始排程）
                if (originalSchedulesToUpdate.Any())
                {
                    await db.Updateable(originalSchedulesToUpdate).UpdateColumns(it => new { it.FPLAN_QTY }).ExecuteCommandAsync();
                }

                // 重置复用排程的来源报工ID
                if (reusedParentSchedulesToReset.Any())
                {
                    await db.Updateable(reusedParentSchedulesToReset)
                            .SetColumns(it => new T_MESD_CRAFT_SCHEDULE { FSOURCE_JOB_BOOKING_ID = null })
                            .ExecuteCommandAsync();
                }

                if (scheduleStatuses.Any()) await db.Updateable(scheduleStatuses).ExecuteCommandAsync();
                if (workOrderStatuses.Any()) await db.Updateable(workOrderStatuses).ExecuteCommandAsync();
                if (workCraftStatuses.Any()) await db.Updateable(workCraftStatuses).UpdateColumns(it => new { it.FFINISH_QTY, it.FRELEASE_QTY }).ExecuteCommandAsync();

                if (workInIds.Any())
                {
                    await db.Deleteable<T_MESD_WORK_IN>().Where(p => workInIds.Contains(p.FWORK_IN_ID)).ExecuteCommandAsync();
                    await db.Deleteable<T_MESD_WORK_IN_PRODUCT>().Where(p => workInIds.Contains(p.FWORK_IN_ID)).ExecuteCommandAsync();
                    await db.Deleteable<T_MESD_WORK_IN_STATUS>().Where(p => workInIds.Contains(p.FWORK_IN_ID)).ExecuteCommandAsync();
                }

                await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(jobBookingsToCancel).ExecuteCommandAsync();

                if (newWorkingJobs.Any())
                {
                    await db.Insertable(newWorkingJobs).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID }).ExecuteCommandAsync();
                }

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            // 步骤 5: 记录日志并返回
            SaveOperateLogsAsync(jobBookingsToCancel, IModuleServices.MES007_JobBooking.Models.OperateType.cancelfinish, oldJobData);

            dataResult.Entity = newWorkingJobs.Select(j => j.FCRAFT_JOB_BOOKING_ID).ToList();
            dataResult.StatusCode = 200;
            return await OK(dataResult);
        }

        /// <summary>
        /// 取消委外完工
        /// </summary>
        /// <returns></returns>
        public async Task<DataResult<List<string>>> CancelOutCompletion(List<string> ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();
            DataResult<List<string>> dataResult = new DataResult<List<string>>();

            //取消完工验证
            var validate = await ValidateCancelCompletionAsync(ids);
            if (validate.StatusCode != 200)
            {
                ERROR(null, 102001, _multiLang["取消开工验证失败.错误信息:" + validate.Message]);
            }

            //获取加工任务
            var jobBookings = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING>().Where(p => ids.Contains(p.FCRAFT_JOB_BOOKING_ID)).ToListAsync();

            var workOrdIds = jobBookings.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();
            //判断工单是否已结案
            var workOrds = await db.Queryable<T_MESD_WORK_ORDER_STATUS>().Where(p => workOrdIds.Contains(p.FWORK_ORDER_ID) && (p.FIF_CLOSE == 1 || p.FIF_CLOSE == 3)).Select(p => p.FWORK_ORDER_ID).ToListAsync();

            var oldJobdata = JsonConvert.DeserializeObject<List<T_MESD_CRAFT_JOB_BOOKING>>(JsonConvert.SerializeObject(jobBookings));
            //获取排程任务状态
            var scheduleIds = jobBookings.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            var scheduleStatus = await db.Queryable<T_MESD_CRAFT_SCHEDULE_STATUS>().Where(p => scheduleIds.Contains(p.FCRAFT_SCHEDULE_ID)).ToListAsync();

            //处理排程任务
            Task.Run(() =>
            {
                scheduleStatus.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
                {
                    var jobBooking = jobBookings.Where(p => p.FCRAFT_SCHEDULE_ID == item.FCRAFT_SCHEDULE_ID).GroupBy(p => p.FCRAFT_SCHEDULE_ID).Select(p => new T_MESD_CRAFT_JOB_BOOKING() { FPASS_QTY = p.Sum(p => p.FPASS_QTY), FNG_QTY = p.Sum(p => p.FNG_QTY), FPASS_WEIGHT = p.Sum(p => p.FPASS_WEIGHT), FNG_WEIGHT = p.Sum(p => p.FNG_WEIGHT) }).FirstOrDefault();

                    item.FPASS_QTY -= jobBooking.FPASS_QTY;
                    item.FNG_QTY -= jobBooking.FNG_QTY;
                    item.FPASS_WEIGHT -= jobBooking.FPASS_WEIGHT;
                    item.FNG_WEIGHT -= jobBooking.FNG_WEIGHT;

                    item.FFINISH_QTY -= jobBooking.FPASS_QTY;
                    item.FFINISH_QTY -= jobBooking.FNG_QTY;
                    item.FFINISH_WEIGHT -= jobBooking.FPASS_WEIGHT;
                    item.FFINISH_WEIGHT -= jobBooking.FNG_WEIGHT;

                    if (item.FIF_CLOSE == 1 || item.FIF_CLOSE == 3)
                    {
                        item.FIF_CLOSE = 2;
                        item.FCLOSEDATE = _iauth.GetCurDateTime();
                        item.FCLOSER = user.UserPsnName;
                        item.FCLOSER_ID = user.UserPsnId;
                    }
                });
            }).Wait();

            //获取工单工艺状态表
            var workCraftIds = jobBookings.Select(p => p.FWORK_ORDER_CRAFT_ID).Distinct().ToList();
            var workCrafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT_STATUS>().Where(p => workCraftIds.Contains(p.FWORK_ORDER_CRAFT_ID)).ToListAsync();

            //处理工单工艺状态
            Task.Run(() =>
            {
                workCrafts.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
                {
                    var jobBooking = jobBookings.Where(p => p.FWORK_ORDER_CRAFT_ID == item.FWORK_ORDER_CRAFT_ID).GroupBy(p => p.FCRAFT_SCHEDULE_ID).Select(p => new T_MESD_CRAFT_JOB_BOOKING() { FPASS_QTY = p.Sum(p => p.FPASS_QTY), FNG_QTY = p.Sum(p => p.FNG_QTY), FPASS_WEIGHT = p.Sum(p => p.FPASS_WEIGHT), FNG_WEIGHT = p.Sum(p => p.FNG_WEIGHT) }).FirstOrDefault();

                    item.FFINISH_QTY -= jobBooking.FPASS_QTY;
                    item.FFINISH_QTY -= jobBooking.FNG_QTY;
                });

            }).Wait();

            //工单产品入库处理
            var workInProduct = await db.Queryable<T_MESD_WORK_IN_PRODUCT>().Where(p => ids.Contains(p.FCRAFT_JOB_BOOKING_ID)).ToListAsync();
            var fworkInIds = workInProduct.Select(p => p.FWORK_IN_ID).ToList();

            //判断是否  最后一道工艺
            var workOrdCrafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT>().Where(p => workOrdIds.Contains(p.FWORK_ORDER_ID)).ToListAsync();
            ConcurrentBag<T_MESD_CRAFT_JOB_BOOKING> concurrentBag = new ConcurrentBag<T_MESD_CRAFT_JOB_BOOKING>();
            jobBookings.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var lastCraft = workOrdCrafts.Where(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID).OrderBy(p => p.FSHOW_SEQNO).LastOrDefault();
                if (item.FWORK_ORDER_CRAFT_ID == lastCraft.FWORK_ORDER_CRAFT_ID)
                {
                    concurrentBag.Add(item);
                }
            });

            //生产工单状态
            var workOrdStatus = await db.Queryable<T_MESD_WORK_ORDER_STATUS>().Where(p => concurrentBag.Select(c => c.FWORK_ORDER_ID).Contains(p.FWORK_ORDER_ID)).ToListAsync();
            workOrdStatus.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var jobBooking = concurrentBag.ToList().Where(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID).GroupBy(p => p.FWORK_ORDER_ID).Select(p => new T_MESD_CRAFT_JOB_BOOKING() { FPASS_QTY = p.Sum(p => p.FPASS_QTY), FNG_QTY = p.Sum(p => p.FNG_QTY), FPASS_WEIGHT = p.Sum(p => p.FPASS_WEIGHT), FNG_WEIGHT = p.Sum(p => p.FNG_WEIGHT) }).FirstOrDefault();

                item.FFINISH_QTY -= jobBooking.FPASS_QTY;
                item.FFINISH_QTY -= jobBooking.FNG_QTY;
                if (item.FIF_CLOSE == 1 || item.FIF_CLOSE == 3)
                {
                    item.FIF_CLOSE = 2;
                    item.FCLOSEDATE = _iauth.GetCurDateTime();
                    item.FCLOSER = user.UserPsnName;
                    item.FCLOSER_ID = user.UserPsnId;
                }
            });



            //处理加工任务
            jobBookings.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                item.FWORK_STATUS = "working";
                item.FPASS_QTY = 0;
                item.FNG_QTY = 0;
                item.FPASS_WEIGHT = 0;
                item.FNG_WEIGHT = 0;
                item.FACT_USE_HOUR = 0;

            });
            db = _isugar.DB;
            db.BeginTran();
            try
            {
                if (scheduleStatus.Count > 0)
                {
                    await db.Updateable<T_MESD_CRAFT_SCHEDULE_STATUS>(scheduleStatus).WhereColumns(it => new { it.FCRAFT_SCHEDULE_STATUS_ID }).ExecuteCommandAsync();
                }

                if (workCrafts.Count > 0)
                {
                    await db.Updateable<T_MESD_WORK_ORDER_CRAFT_STATUS>(workCrafts).WhereColumns(it => new { it.FWORK_ORDER_CRAFT_STATUS_ID }).ExecuteCommandAsync();

                }

                if (fworkInIds.Count > 0)
                {
                    await db.Deleteable<T_MESD_WORK_IN>().Where(p => fworkInIds.Contains(p.FWORK_IN_ID)).ExecuteCommandAsync();

                    await db.Deleteable<T_MESD_WORK_IN_PRODUCT>().Where(p => fworkInIds.Contains(p.FWORK_IN_ID)).ExecuteCommandAsync();
                    await db.Deleteable<T_MESD_WORK_IN_STATUS>().Where(p => fworkInIds.Contains(p.FWORK_IN_ID)).ExecuteCommandAsync();

                }


                if (workOrdStatus.Count > 0)
                {
                    await db.Updateable<T_MESD_WORK_ORDER_STATUS>(workOrdStatus).WhereColumns(it => new { it.FWORK_ORDER_STATUS_ID }).ExecuteCommandAsync();

                }
                if (jobBookings.Count > 0)
                {
                    await db.Updateable<T_MESD_CRAFT_JOB_BOOKING>(jobBookings).WhereColumns(it => new { it.FCRAFT_JOB_BOOKING_ID }).ExecuteCommandAsync();
                }

                db.CommitTran();
            }
            catch (Exception)
            {
                db.RollbackTran();
                throw;
            }

            SaveOperateLogsAsync(jobBookings, IModuleServices.MES007_JobBooking.Models.OperateType.cancelfinish, oldJobdata);

            var schDataIds = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                    ((sch, schsts) => new JoinQueryInfos(JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schsts.FCRAFT_SCHEDULE_ID))
                    .Where((sch, schsts) => scheduleIds.Contains(sch.FCRAFT_SCHEDULE_ID))
                    .Where((sch, schsts) => schsts.FFINISH_QTY >= sch.FPLAN_QTY)
                    .Select((sch, schsts) => sch.FCRAFT_SCHEDULE_ID)
                    .ToListAsync();
            if (schDataIds.Count > 0)
                await DeleteWorkJobBooKingAsync(schDataIds);

            dataResult.Entity = ids;
            dataResult.StatusCode = 200;
            return await OK(dataResult);
        }


        /// <summary>
        /// 保存日志
        /// </summary>
        /// <param name="daoJob"></param>
        /// <param name="log"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        private async Task SaveOperateLogsAsync(List<T_MESD_CRAFT_JOB_BOOKING> daoJobBooking, IModuleServices.MES007_JobBooking.Models.OperateType operateType, List<T_MESD_CRAFT_JOB_BOOKING> oldData)
        {
            ISqlSugarClient db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            ConcurrentBag<T_MESD_CRAFT_JOB_BOOKING_LOG> concurrentBag = new ConcurrentBag<T_MESD_CRAFT_JOB_BOOKING_LOG>();
            daoJobBooking.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                T_MESD_CRAFT_JOB_BOOKING_LOG daoLog = new T_MESD_CRAFT_JOB_BOOKING_LOG()
                {
                    FOPERATE_TYPE = operateType.ToString(),
                    FCRAFT_JOB_BOOKING_ID = item.FCRAFT_JOB_BOOKING_ID,

                    FCRAFT_JOB_BOOKING_LOG_ID = GuidHelper.NewGuid(),
                    FCRAFT_SCHEDULE_ID = item.FCRAFT_SCHEDULE_ID,

                    FOPERATE_LOG = JsonConvert.SerializeObject(oldData.FirstOrDefault(p => p.FCRAFT_JOB_BOOKING_ID == item.FCRAFT_JOB_BOOKING_ID)),
                };
                concurrentBag.Add(daoLog);

            });

            var daoJobs = concurrentBag.ToList();
            await db.Insertable<T_MESD_CRAFT_JOB_BOOKING_LOG>(daoJobs).IgnoreColumns(p => new { p.FMODIDATE, p.FMODIFIER, p.FMODIFIER_ID })
                .ExecuteCommandAsync();
        }


        /// <summary>
        ///处理登记作业数据
        /// </summary>
        async Task<RecordStockBillModel> RecordStockBillAsync(List<string> ids, IModuleServices.STK005_Stock.Models.OperateType operateType, SqlSugarClient db)
        {

            var recordStockBillModel = new RecordStockBillModel();

            var allots = await db.Queryable<T_MESD_WORK_IN>().Where(p => ids.Contains(p.FWORK_IN_ID)).ToListAsync();

            var allotsMat = await db.Queryable<T_MESD_WORK_IN_PRODUCT>().Where(p => ids.Contains(p.FWORK_IN_ID)).ToListAsync();

            //判断仓库和货位
            var storeInfos = await _businessService.GetStoresAsync(allotsMat.Select(p => p.FSTORE_ID).Distinct().ToList());
            var isEnablePlace = storeInfos.Entity.Where(p => p.FIF_ENABLE_PLACE).Select(p => p.FSTORE_ID).ToList();



            var iORecords = new ConcurrentBag<IORecordModel>();

            allots.AsParallel().WithDegreeOfParallelism(4).ForAll(item =>
            {
                var ioCord = new IORecordModel();
                ioCord.FBILL_ID = item.FWORK_IN_ID;
                ioCord.FBILL_NO = item.FWORK_IN_NO;
                ioCord.FBILL_DATE = item.FBILL_DATE;
                ioCord.FBUS_TYPE = item.FBUS_TYPE;
                ioCord.FEMP_ID = item.FEMP_ID;
                ioCord.FDEPT_ID = item.FDEPT_ID;
                ioCord.FREMARK = item.FREMARK;

                ioCord.Materials = new List<IORecordMaterialModel>();

                var allotsMats = allotsMat.Where(p => p.FWORK_IN_ID == item.FWORK_IN_ID).ToList();

                foreach (var mat in allotsMats)
                {
                    if (string.IsNullOrEmpty(mat.FSTORE_ID))
                    {
                        continue;
                    }

                    if (isEnablePlace.Contains(mat.FSTORE_ID) && string.IsNullOrEmpty(mat.FSTORE_PLACE_ID))
                    {
                        continue;
                    }


                    var iORecord = new IORecordMaterialModel();
                    iORecord.FSHOW_SEQNO = mat.FSHOW_SEQNO;
                    iORecord.FSTORE_ID = mat.FSTORE_ID;
                    iORecord.FSTORE_PLACE_ID = mat.FSTORE_PLACE_ID;
                    iORecord.FMATERIAL_ID = mat.FMATERIAL_ID;
                    iORecord.FSTK_UNIT_ID = mat.FSTK_UNIT_ID;
                    iORecord.FSTK_UNIT_QTY = mat.FSTK_UNIT_QTY;
                    iORecord.FUNIT_STK_CONVERT_SCALE = mat.FUNIT_STK_CONVERT_SCALE;
                    iORecord.FPRICE = mat.FUP;
                    iORecord.FAMT = mat.FAMT;
                    iORecord.FUNIT_ID = mat.FUNIT_ID;
                    iORecord.FUNIT_QTY = mat.FUNIT_QTY;
                    iORecord.FREMARK = mat.FREMARK;
                    iORecord.FBILL_DETAIL_ID = mat.FWORK_IN_PRODUCT_ID;
                    iORecord.FBILL_ID = mat.FWORK_IN_ID;
                    iORecord.FIF_OUT_DIRECTION = false;
                    iORecord.FBILL_UNIT_ID = mat.FPRO_UNIT_ID;
                    iORecord.FBILL_UNIT_QTY = mat.FQTY;
                    iORecord.FUNIT_BILL_CONVERT_SCALE = mat.FUNIT_PRO_CONVERT_SCALE;
                    ioCord.Materials.Add(iORecord);
                }
                if (ioCord.Materials.Count > 0)
                {
                    iORecords.Add(ioCord);
                }
            });





            recordStockBillModel.OperateType = operateType;
            recordStockBillModel.RecordModels = iORecords.ToList();

            return recordStockBillModel;
        }



        /// <summary>
        ///登记库存作业
        /// </summary>
        async Task<DataResult<bool>> RecordStockBillRpcAsync(RecordStockBillModel recordStockBillModel)
        {

            var dataResult = new DataResult<bool>()
            {
                Entity = true,
                StatusCode = 200
            };

            var rpcServer = this.GetService<ISTK005StockService>("STK005Stock");
            var rpcrtn = await rpcServer.RecordStockBillAsync(recordStockBillModel);
            if (rpcrtn.StatusCode != 200)
            {
                dataResult.Entity = false;
            }
            return dataResult;
        }

        /// <summary>
        /// 按id返回加工任务
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private async Task<List<CraftJobBookingModel>> GetJobBookingByIdsAsync(List<string> ids)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            //取出进行中的加工任务
            List<CraftJobBookingModel> bookingModel = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                    ((job, sch, schStatus) => new JoinQueryInfos(
                        JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                        JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                .Where((job, sch, schStatus) => ids.Contains(job.FCRAFT_JOB_BOOKING_ID))
                .Select((job, sch, schStatus) => new CraftJobBookingModel
                {
                    FACT_ED_DATE = job.FACT_ED_DATE,
                    FACT_ST_DATE = job.FACT_ST_DATE,

                    FACT_USE_HOUR = job.FACT_USE_HOUR,

                    FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                    FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                    FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                    FEMP_ID = job.FEMP_ID,

                    FEMP_NAME = job.FEMP_NAME,
                    FLAST_PAUSE_DATE = job.FLAST_PAUSE_DATE,

                    FLAST_RESUME_DATE = job.FLAST_RESUME_DATE,
                    FNG_QTY = job.FNG_QTY,

                    FPASS_QTY = job.FPASS_QTY,
                    FWORK_STATUS = job.FWORK_STATUS,
                    FCRAFT_STATUS = job.FWORK_STATUS,

                    FCRAFT_ID = job.FCRAFT_ID,
                    FMATERIAL_ID = job.FMATERIAL_ID,

                    FSALE_ORDER_ID = job.FSALE_ORDER_ID,
                    FSTATION_ID = job.FSTATION_ID,

                    FWORK_ORDER_CRAFT_ID = job.FWORK_ORDER_CRAFT_ID,
                    FWORK_ORDER_ID = job.FWORK_ORDER_ID,

                    FNG_WEIGHT = job.FNG_WEIGHT,
                    FPASS_WEIGHT = job.FPASS_WEIGHT,

                    FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,
                    FPLAN_QTY_SCHEDULE = sch.FPLAN_QTY,

                    FPLAN_WEIGHT_SCHEDULE = sch.FPLAN_WEIGHT,
                    FFINISH_WEIGHT_SCHEDULE = schStatus.FFINISH_WEIGHT

                })
                .ToListAsync();

            return bookingModel;
        }


        /// <summary>
        /// 查询待加工排程任务--悠悠工位机排程  去掉已完成的排程信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<DataResult<object>> QueryWaitJobsChuangPaiAsync(QueryRequestModel model)
        {
            var db = _isugar.DB;
            var user = await _iauth.GetUserAccountAsync();

            if (model == null)
            {
                ERROR(null, 100001, string.Format(_multiLang["传入参数 {0} 为空."], "model"));
            }

            //总行数 
            RefAsync<int> totalSize = new RefAsync<int>();

            //清除传入字段为空的, MES003报表服务调此接口，传了空字段。MES003暂不能更新给鑫海，先在这里修改
            if (model.WhereGroup != null && model.WhereGroup.Items != null)
            {
                model.WhereGroup.Items.RemoveAll(p => string.IsNullOrWhiteSpace(p.FieldName));
            }

            WhereCollections wheres = WhereCollections.FromQueryRequestModel(model);


            //查询
            var woCraftModelAll = await db.Queryable<T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS,
                                    T_MESD_WORK_ORDER, T_MESD_WORK_ORDER_STATUS, T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS, T_MESM_CRAFT>(
                                 (sch, schStatus, wo, woStatus, craft, craftStatus, craftlog) =>
                                 new JoinQueryInfos(
                                 JoinType.Left, sch.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == wo.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_ID == woStatus.FWORK_ORDER_ID,
                                 JoinType.Left, sch.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == craftStatus.FWORK_ORDER_CRAFT_ID,
                                 JoinType.Left, craftlog.FCRAFT_ID == craft.FCRAFT_ID

                                 ))
             //.WhereIF(!string.IsNullOrWhiteSpace(stationId), (sch, schStatus, wo, woStatus, craft, craftStatus) => sch.FSTATION_ID == stationId)             
             .Where((sch, schStatus, wo, woStatus, craft, craftStatus) =>
             //(schStatus.FIF_CLOSE == 0 || schStatus.FIF_CLOSE == 2) && 
             schStatus.FRELEASE_STATUS == true
               && wo.FBOOKING_TYPE == _wocraft //按工单工艺报工

                                                                           //&& sch.FPLAN_QTY > schStatus.FFINISH_QTY
                                                                           )
             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus, craftlog) => schStatus.FACT_ST_DATE, OrderByType.Desc)
             .OrderBy((sch, schStatus, wo, woStatus, craft, craftStatus, craftlog) => sch.FPLAN_ST_DATE, OrderByType.Asc)

             .Select((sch, schStatus, wo, woStatus, craft, craftStatus, craftlog) => new WOCraftScheduleModel()
             {
                 FREMARK = craft.FREMARK,
                 FECODE = sch.FECODE,

                 FFINISH_QTY = craftStatus.FFINISH_QTY,

                 FIF_CLOSE = schStatus.FIF_CLOSE,
                 FLEVEL = wo.FLEVEL,

                 FMATERIAL_ID = sch.FMATERIAL_ID,

                 FPARENT_MATERIAL_ID = wo.FPARENT_MATERIAL_ID,

                 FCRAFT_LINE_ID = wo.FCRAFT_LINE_ID,

                 FPARENT_WORK_ORDER_ID = wo.FPARENT_WORK_ORDER_ID,
                 FPLAN_EMP_ID = sch.FPLAN_EMP_ID,

                 FPRO_QTY = wo.FPRO_QTY,
                 FPRO_UNIT_ID = wo.FPRO_UNIT_ID,

                 FSALE_ORDER_ID = wo.FSALE_ORDER_ID,
                 FSALE_ORDER_PRODUCT_ID = wo.FSALE_ORDER_PRODUCT_ID,

                 FWORK_ORDER_ID = sch.FWORK_ORDER_ID,
                 FWORK_ORDER_TYPE = wo.FWORK_ORDER_TYPE,

                 FCRAFT_ID = sch.FCRAFT_ID,
                 FGEN_RATE = craft.FGEN_RATE,

                 FPROCESS_FEE = craft.FPROCESS_FEE,
                 FSHOW_SEQNO = craft.FSHOW_SEQNO,

                 FSTATION_ID = sch.FSTATION_ID,
                 FWORK_ORDER_CRAFT_ID = sch.FWORK_ORDER_CRAFT_ID,
                 FWORK_ORDER_NO = wo.FWORK_ORDER_NO,

                 FWAIT_QTY = (wo.FPRO_QTY - craftStatus.FPLAN_QTY),
                 FPLAN_QTY = sch.FPLAN_QTY,
                 FORI_PLAN_QTY = sch.FORI_PLAN_QTY,

                 FPLAN_ED_DATE = sch.FPLAN_ED_DATE,
                 FPLAN_ST_DATE = sch.FPLAN_ST_DATE,

                 FCRAFT_SCHEDULE_ID = sch.FCRAFT_SCHEDULE_ID,

                 FCRAFT_SCHEDULE_NO = sch.FCRAFT_SCHEDULE_NO,
                 FQRCODE = sch.FQRCODE,

                 FPLAN_USE_HOUR = sch.FPLAN_USE_HOUR,

                 FRELEASE_STATUS = schStatus.FRELEASE_STATUS,
                 FSCHEDULE_BATCH_ID = sch.FSCHEDULE_BATCH_ID,

                 FCRAFT_SCHEDULE_STATUS_ID = schStatus.FCRAFT_SCHEDULE_STATUS_ID,

                 FCLOSEDATE = schStatus.FCLOSEDATE,
                 FCLOSER = schStatus.FCLOSER,
                 FCLOSER_ID = schStatus.FCLOSER_ID,

                 FRELEASER = schStatus.FRELEASER,
                 FRELEASER_ID = schStatus.FRELEASER_ID,
                 FRELEASE_DATE = schStatus.FRELEASE_DATE,

                 FRELEASE_QTY = craftStatus.FRELEASE_QTY,

                 FCREATOR = sch.FCREATOR,
                 FCREATOR_ID = sch.FCREATOR_ID,
                 FCDATE = sch.FCDATE,

                 FMODIDATE = sch.FMODIDATE,
                 FMODIFIER = sch.FMODIFIER,
                 FMODIFIER_ID = sch.FMODIFIER_ID,

                 FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,   //排程任务的完工数量
                 FPASS_QTY_SCHEDULE = schStatus.FPASS_QTY,   //排程任务的合格数
                 FNG_QTY_SCHEDULE = schStatus.FNG_QTY,    //排程任务的不良数

                 FACT_ST_DATE = schStatus.FACT_ST_DATE,   //排程任务的实际开工
                 FACT_ED_DATE = schStatus.FACT_ED_DATE,   //排程任务的实际完工
                 FACT_USE_HOUR = schStatus.FACT_USE_HOUR,  //排程任务的实际工时

                 //重量
                 FWEIGHT_UNIT_ID = sch.FWEIGHT_UNIT_ID,
                 FPLAN_WEIGHT = sch.FPLAN_WEIGHT,
                 FUNIT_WEIGHT = sch.FUNIT_WEIGHT,

                 SCHEDULE_FSHOW_SEQNO = sch.SCHEDULE_FSHOW_SEQNO,
                 FLOT_NO = sch.FLOT_NO,
                 FIF_TAG = craftlog.FIF_TAG,
                 FTAG_TYPE = craftlog.FTAG_TYPE,
                 FTAG_REPORTID = craftlog.FTAG_REPORTID
             })
               .Where(wheres)
               .ToListAsync();
            // .ToPageListAsync(model.PageIndex, model.PageSize, totalSize);

            var workOrdIds = woCraftModelAll.Select(p => p.FWORK_ORDER_ID).Distinct().ToList();


            var woCraftModels = new List<WOCraftScheduleModel>();

            woCraftModels = woCraftModelAll;

            //   var returnmodel = new List<WOCraftScheduleModel>();//新的列表


            //if (workOrdIds.Count > 0)
            //{

            //    //检查前一工艺是否有完工记录
            //    var woCrafts = await db.Queryable<T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>
            //        ((craft, status) => new JoinQueryInfos(JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == status.FWORK_ORDER_CRAFT_ID))
            //        .Where((craft, status) => workOrdIds.Contains(craft.FWORK_ORDER_ID))
            //        .Select<SimpleWorkOrderCraftModel>()
            //        .ToListAsync();

            //    foreach (var item in woCraftModelAll)
            //    {

            //        // woCraftModels.Add(item);

            //        var shangyicraft = woCrafts.Where(p => p.FWORK_ORDER_ID == item.FWORK_ORDER_ID && p.FSHOW_SEQNO < item.FSHOW_SEQNO).OrderBy(p => p.FSHOW_SEQNO).LastOrDefault();

            //        if (shangyicraft == null)//没有上一工艺 就是他自己
            //        {
            //            woCraftModels.Add(item);
            //        }
            //        else
            //        {
            //            if (shangyicraft.FFINISH_QTY >= shangyicraft.FPLAN_QTY) //完工数大于计划数 说明上一工艺已完工
            //            {
            //                woCraftModels.Add(item);
            //            }
            //        }
            //    }
            //}


            //woCraftModelAll = returnmodel;


            var craftSchIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            var allStationIds = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
            var craftLineIds = woCraftModels.Select(p => p.FCRAFT_LINE_ID).Distinct().ToList();

            var craftLines = await db.Queryable<T_MESM_CRAFT_LINE>()
                    .Where(p => craftLineIds.Contains(p.FCRAFT_LINE_ID))
                    .ToListAsync();



            if (woCraftModels.Count > 0)
            {

                var jobDatas = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                       ((job, sch, schStatus) => new JoinQueryInfos(
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                           JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                   .Where((job, sch, schStatus) => craftSchIds.Contains(job.FCRAFT_SCHEDULE_ID) && allStationIds.Contains(job.FSTATION_ID)
                       && (job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                           job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                           job.FWORK_STATUS == WorkStatus.working.ToString() ||
                           job.FWORK_STATUS == WorkStatus.finished.ToString()))
                   .OrderBy((job, sch, schStatus) => job.FCDATE, OrderByType.Desc)
                   .Select((job, sch, schStatus) => new CraftJobBookingModel
                   {
                       FCDATE = job.FCDATE,
                       FACT_ED_DATE = job.FACT_ED_DATE,
                       FACT_ST_DATE = job.FACT_ST_DATE,

                       FACT_USE_HOUR = job.FACT_USE_HOUR,

                       FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                       FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                       FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                       FEMP_ID = job.FEMP_ID,

                       FEMP_NAME = job.FEMP_NAME,
                       FLAST_PAUSE_DATE = job.FLAST_PAUSE_DATE,

                       FLAST_RESUME_DATE = job.FLAST_RESUME_DATE,
                       FNG_QTY = job.FNG_QTY,

                       FPASS_QTY = job.FPASS_QTY,
                       FWORK_STATUS = job.FWORK_STATUS,

                       FCRAFT_ID = job.FCRAFT_ID,
                       FMATERIAL_ID = job.FMATERIAL_ID,

                       FSALE_ORDER_ID = job.FSALE_ORDER_ID,
                       FSTATION_ID = job.FSTATION_ID,

                       FWORK_ORDER_CRAFT_ID = job.FWORK_ORDER_CRAFT_ID,
                       FWORK_ORDER_ID = job.FWORK_ORDER_ID,

                       FNG_WEIGHT = job.FNG_WEIGHT,
                       FPASS_WEIGHT = job.FPASS_WEIGHT,

                       FPLAN_QTY_SCHEDULE = sch.FPLAN_QTY,
                       FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,
                   }).ToListAsync();



                ////检查前一工艺是否有完工记录
                //var woCrafts = (await db.Queryable<T_MESD_WORK_ORDER_CRAFT, T_MESD_WORK_ORDER_CRAFT_STATUS>
                //    ((craft, status) => new JoinQueryInfos(JoinType.Left, craft.FWORK_ORDER_CRAFT_ID == status.FWORK_ORDER_CRAFT_ID))
                //    .Where((craft, status) => workOrdIds.Contains(craft.FWORK_ORDER_ID))
                //    .Select<SimpleWorkOrderCraftModel>()
                //    .ToListAsync())
                //    .OrderBy(p => p.FWORK_ORDER_ID)
                //    .OrderBy(p => p.FSHOW_SEQNO).ToList();

                //var jobDatas = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING, T_MESD_CRAFT_SCHEDULE, T_MESD_CRAFT_SCHEDULE_STATUS>
                //       ((job, sch, schStatus) => new JoinQueryInfos(
                //           JoinType.Left, job.FCRAFT_SCHEDULE_ID == sch.FCRAFT_SCHEDULE_ID,
                //           JoinType.Left, job.FCRAFT_SCHEDULE_ID == schStatus.FCRAFT_SCHEDULE_ID))
                //   .Where((job, sch, schStatus) => craftSchIds.Contains(job.FCRAFT_SCHEDULE_ID) && allStationIds.Contains(job.FSTATION_ID)
                //       && (job.FWORK_STATUS == WorkStatus.paused.ToString() ||
                //           job.FWORK_STATUS == WorkStatus.partfinished.ToString() ||
                //           job.FWORK_STATUS == WorkStatus.working.ToString() ||
                //           job.FWORK_STATUS == WorkStatus.finished.ToString()))
                //   .OrderBy((job, sch, schStatus) => job.FCDATE, OrderByType.Desc)
                //   .Select((job, sch, schStatus) => new CraftJobBookingModel
                //   {
                //       FCDATE = job.FCDATE,
                //       FACT_ED_DATE = job.FACT_ED_DATE,
                //       FACT_ST_DATE = job.FACT_ST_DATE,

                //       FACT_USE_HOUR = job.FACT_USE_HOUR,

                //       FCRAFT_JOB_BOOKING_ID = job.FCRAFT_JOB_BOOKING_ID,
                //       FCRAFT_JOB_BOOKING_NO = job.FCRAFT_JOB_BOOKING_NO,

                //       FCRAFT_SCHEDULE_ID = job.FCRAFT_SCHEDULE_ID,
                //       FEMP_ID = job.FEMP_ID,

                //       FEMP_NAME = job.FEMP_NAME,
                //       FLAST_PAUSE_DATE = job.FLAST_PAUSE_DATE,

                //       FLAST_RESUME_DATE = job.FLAST_RESUME_DATE,
                //       FNG_QTY = job.FNG_QTY,

                //       FPASS_QTY = job.FPASS_QTY,
                //       FWORK_STATUS = job.FWORK_STATUS,

                //       FCRAFT_ID = job.FCRAFT_ID,
                //       FMATERIAL_ID = job.FMATERIAL_ID,

                //       FSALE_ORDER_ID = job.FSALE_ORDER_ID,
                //       FSTATION_ID = job.FSTATION_ID,

                //       FWORK_ORDER_CRAFT_ID = job.FWORK_ORDER_CRAFT_ID,
                //       FWORK_ORDER_ID = job.FWORK_ORDER_ID,

                //       FNG_WEIGHT = job.FNG_WEIGHT,
                //       FPASS_WEIGHT = job.FPASS_WEIGHT,

                //       FPLAN_QTY_SCHEDULE = sch.FPLAN_QTY,
                //       FFINISH_QTY_SCHEDULE = schStatus.FFINISH_QTY,
                //   }).ToListAsync();

                //var craftIds = woCrafts.Select(p => p.FCRAFT_ID).Distinct().ToList();
                //var craftResult = await RpcGetCraftsAsync(craftIds);
                //if (craftResult.StatusCode != 200)
                //{
                //    ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                //}

                //woCrafts.AsParallel().WithDegreeOfParallelism(1).ForAll(item =>
                //{
                //    var craftData = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == item.FCRAFT_ID);
                //    if (craftData != null)
                //    {
                //        item.FCRAFT_CODE = craftData.FCRAFT_CODE;
                //        item.FCRAFT_NAME = craftData.FCRAFT_NAME;
                //    }
                //});

                //woCraftModelAll.AsParallel().WithDegreeOfParallelism(1).ForAll(craft =>
                //    {
                //        var craftLineInfo = craftLines.FirstOrDefault(p=> p.FCRAFT_LINE_ID==craft.FCRAFT_LINE_ID);
                //        if(craftLineInfo != null)
                //        {
                //            craft.FCRAFT_LINE_NAME = craftLineInfo.FCRAFT_LINE_NAME;
                //        }

                //        var craftWorks = woCrafts.Where(p => p.FWORK_ORDER_ID == craft.FWORK_ORDER_ID)
                //                        .OrderBy(p => p.FSHOW_SEQNO)
                //                        .Distinct()
                //                        .ToList();

                //        var schCraft = craftWorks.FirstOrDefault(p => p.FWORK_ORDER_CRAFT_ID == craft.FWORK_ORDER_CRAFT_ID);

                //        if (craftWorks.IndexOf(schCraft) > 0)
                //        {
                //            var preWoCraft = craftWorks[craftWorks.IndexOf(schCraft) - 1];
                //            var preFinishQty = craftWorks.Where(p => p.FWORK_ORDER_CRAFT_ID == preWoCraft.FWORK_ORDER_CRAFT_ID)
                //                                        .Select(p => p.FFINISH_QTY)
                //                                        .FirstOrDefault();

                //            //上一工艺完工数大于0
                //            if (preFinishQty>0)
                //            {
                //                woCraftModels.Add(craft);
                //            }
                //        }
                //        else if (craftWorks.IndexOf(schCraft) == 0)
                //        {
                //            woCraftModels.Add(craft);
                //        }

                //    });

                if (woCraftModels.Count > 0)
                {

                    List<string> materialIds1 = woCraftModels.SelectMany(p => { var ids = new List<string> { p.FMATERIAL_ID, p.FPARENT_MATERIAL_ID }; return ids; }).Where(p => !string.IsNullOrEmpty(p)).Distinct().ToList();
                    //取出物料信息
                    DataResult<List<SimpleMaterialModel>> materialResult = await RpcGetMaterialsAsync(materialIds1);
                    if (materialResult.StatusCode != 200)
                    {
                        ERROR(materialResult, materialResult.StatusCode, materialResult.Message);
                    }
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                    {


                        var craftLineInfo = craftLines.FirstOrDefault(p => p.FCRAFT_LINE_ID == craft.FCRAFT_LINE_ID);
                        if (craftLineInfo != null)
                        {
                            craft.FCRAFT_LINE_NAME = craftLineInfo.FCRAFT_LINE_NAME;
                        }


                        var material = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == craft.FMATERIAL_ID);
                        if (material != null)
                        {
                            craft.FMATERIAL_CODE = material.FMATERIAL_CODE;
                            craft.FMATERIAL_NAME = material.FMATERIAL_NAME;
                            craft.FSPEC_DESC = material.FSPEC_DESC;
                            craft.FPIC_ATTACH_ID = material.FPIC_ATTACH_ID;
                            craft.FGOODS_MODEL = material.FGOODS_MODEL;
                        }

                        if (!string.IsNullOrEmpty(craft.FPARENT_MATERIAL_ID))
                        {
                            var parentMat = materialResult.Entity.FirstOrDefault(p => p.FMATERIAL_ID == craft.FPARENT_MATERIAL_ID);
                            if (parentMat != null)
                            {
                                craft.FPARENT_MATERIAL_CODE = parentMat.FMATERIAL_CODE;
                                craft.FPARENT_MATERIAL_NAME = parentMat.FMATERIAL_NAME;
                            }
                        }



                        if (jobDatas.Count > 0)
                        {
                            var jobInfos = jobDatas.Where(p => p.FCRAFT_SCHEDULE_ID == craft.FCRAFT_SCHEDULE_ID).ToList();
                            if (jobInfos.Count > 0)
                            {
                                var jobinfo = jobInfos.OrderByDescending(p => p.FCDATE).FirstOrDefault();
                                craft.HandlingJobs = new List<CraftJobBookingModel>() { jobinfo };
                            }

                        }

                    });
                    var saleOrderIds = woCraftModels.Select(p => p.FSALE_ORDER_ID).Distinct().ToList();
                    var saleOrdrpcServer = this.GetService<ICOP001SaleOrderService>("COP001SaleOrder");
                    var saleOrderResult = await saleOrdrpcServer.QuerySalesByIdsAsync(saleOrderIds);
                    if (saleOrderResult.StatusCode != 200)
                    {
                        ERROR(saleOrderResult, saleOrderResult.StatusCode, saleOrderResult.Message);
                    }
                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                    {
                        var saleOrd = saleOrderResult.Entity.FirstOrDefault(p => p.FSALE_ORDER_ID == craft.FSALE_ORDER_ID);
                        if (saleOrd != null)
                        {
                            craft.FSALE_ORDER_NO = saleOrd.FSALE_ORDER_NO;
                            craft.FPROJECT_ID = saleOrd.FPROJECT_ID;  //项目
                            craft.FPROJECT_NAME = saleOrd.FPROJECT_NAME;
                            craft.FPROJECT_CODE = saleOrd.FPROJECT_CODE;
                        }
                    });

                    //取计划员姓名
                    var empIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPLAN_EMP_ID)).Select(p => p.FPLAN_EMP_ID).Distinct().ToList();
                    if (empIds.Count > 0)
                    {
                        var empResult = await RpcGetEmployeesAsync(empIds);
                        if (empResult.StatusCode == 200)
                        {
                            woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                            {
                                if (!string.IsNullOrWhiteSpace(craft.FPLAN_EMP_ID))
                                {
                                    var emp = empResult.Entity.FirstOrDefault(p => p.FEMP_ID == craft.FPLAN_EMP_ID);
                                    if (emp != null)
                                    {
                                        craft.FPLAN_EMP_NAME = emp.FEMP_NAME;
                                    }
                                }
                            });
                        }
                        else
                        {
                            ERROR(empResult, empResult.StatusCode, empResult.Message);
                        }
                    }

                    //取出生产单位
                    var unitIds = woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FPRO_UNIT_ID)).Select(p => p.FPRO_UNIT_ID).Distinct().ToList();

                    //重量单位
                    unitIds.AddRange(woCraftModels.Where(p => !string.IsNullOrWhiteSpace(p.FWEIGHT_UNIT_ID))
                                    .Select(p => p.FWEIGHT_UNIT_ID).Distinct().ToList());

                    if (unitIds.Count > 0)
                    {
                        var unitResult = await RpcGetUnitsAsync(unitIds);
                        if (unitResult.StatusCode == 200)
                        {
                            woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(craft =>
                            {
                                //数量单位
                                if (!string.IsNullOrWhiteSpace(craft.FPRO_UNIT_ID))
                                {
                                    var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FPRO_UNIT_ID);
                                    if (unit != null)
                                    {
                                        craft.FPRO_UNIT_CODE = unit.FUNIT_CODE;
                                        craft.FPRO_UNIT_NAME = unit.FUNIT_NAME;
                                    }
                                }

                                //重量单位
                                if (!string.IsNullOrWhiteSpace(craft.FWEIGHT_UNIT_ID))
                                {
                                    var unit = unitResult.Entity.FirstOrDefault(p => p.FUNIT_ID == craft.FWEIGHT_UNIT_ID);
                                    if (unit != null)
                                    {
                                        craft.FWEIGHT_UNIT_NAME = unit.FUNIT_NAME;
                                    }
                                }
                            });
                        }
                        else
                        {
                            ERROR(unitResult, unitResult.StatusCode, unitResult.Message);
                        }
                    }


                    //获取工艺
                    var craftIds = woCraftModels.Select(p => p.FCRAFT_ID).Distinct().ToList();
                    var craftResult = await RpcGetCraftsAsync(craftIds);
                    if (craftResult.StatusCode != 200)
                    {
                        ERROR(craftResult, craftResult.StatusCode, craftResult.Message);
                    }


                    woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                    {
                        var craft = craftResult.Entity.FirstOrDefault(p => p.FCRAFT_ID == woCraft.FCRAFT_ID);
                        if (craft != null)
                        {
                            woCraft.FCRAFT_CODE = craft.FCRAFT_CODE;
                            woCraft.FCRAFT_NAME = craft.FCRAFT_NAME;
                            woCraft.FCRAFT_DESC = craft.FCRAFT_DESC;
                        }
                    });

                    //取出工位
                    var stationIds = woCraftModels.Select(p => p.FSTATION_ID).Distinct().ToList();
                    if (stationIds.Count > 0)
                    {
                        var stationResult = await RpcGetStationsAsync(stationIds);
                        if (stationResult.StatusCode != 200)
                        {
                            ERROR(stationResult, stationResult.StatusCode, stationResult.Message);
                        }
                        woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(woCraft =>
                        {
                            var craft = stationResult.Entity.FirstOrDefault(p => p.FSTATION_ID == woCraft.FSTATION_ID);
                            if (craft != null)
                            {
                                woCraft.FSTATION_CODE = craft.FSTATION_CODE;
                                woCraft.FSTATION_NAME = craft.FSTATION_NAME;
                            }
                        });
                    }

                    //取出投入物料
                    //await GetSubMaterialAsync(woCraftModels);

                    //取出执行人
                    //await GetScheduleEmpsAsync(woCraftModels);

                    //获取排程的加工状态
                    await GetScheduleJinJiStatusAsync(woCraftModels);

                }
                //查询对应的标签信息

                foreach (var item in woCraftModels)
                {
                    item.tagItems = new List<TagItem>();
                    item.tagItems = await db.Queryable<T_MESD_CRAFT_JOB_BOOKING_TAG>().Where(n => n.FCRAFT_SCHEDULE_ID == item.FCRAFT_SCHEDULE_ID).Select<TagItem>().OrderBy(n => n.FBARCODE_NO, OrderByType.Asc).ToListAsync();

                }

            }

            DataResult<object> result = new DataResult<object>
            {
                Entity = woCraftModels.OrderBy(p => p.FMATERIAL_CODE).OrderBy(p => p.SCHEDULE_FSHOW_SEQNO).ToList(),

                StatusCode = 200,
                Pager = new PagerResult
                {
                    TotalRecords = woCraftModels.Count,
                    PageNum = model.PageIndex,
                    PageSize = model.PageSize,
                }
            };

            return await OK(result);
        }


        /// <summary>
        /// 获取排程任务的加工状态
        /// </summary>
        /// <param name="woCraftModels"></param>
        /// <returns></returns>
        private async Task GetScheduleJinJiStatusAsync(List<WOCraftScheduleModel> woCraftModels)
        {

            var schIds = woCraftModels.Select(p => p.FCRAFT_SCHEDULE_ID).Distinct().ToList();
            if (!schIds.Any())
            {
                return;
            }

            var rpcServer = this.GetService<IModuleServices.MES007_JobBooking.IMES007JobQueryService>("MES007JobQuery");
            QueryRequestModel model = new QueryRequestModel()
            {
                PageIndex = 0,
                PageSize = int.MaxValue,
                WhereGroup = new QueryWhereGroupModel
                {
                    GroupType = EnumGroupType.AND,
                    Items = new List<QueryWhereItemModel> {
                            new QueryWhereItemModel{
                                FieldName="booking.FCRAFT_SCHEDULE_ID",
                                OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.In,
                                Value=string.Join(",",schIds),
                                }
                            //,new QueryWhereItemModel{
                            //    FieldName="booking.FWORK_STATUS",
                            //    OperatorType=Core.HCPlatform.Utilities.EnumQuerySymbol.NotEqual,
                            //    Value="cancel",
                            //    }
                        }
                }
            };
            var bookingResult = await rpcServer.QueryJobBookingAsync(model);
            if (bookingResult.StatusCode != 200)
            {
                ERROR(bookingResult, bookingResult.StatusCode, bookingResult.Message);
            }

            var bookingData = bookingResult.Entity;
            woCraftModels.AsParallel().WithDegreeOfParallelism(4).ForAll(p =>
            {
                var bookings = bookingData.Where(j => j.FCRAFT_SCHEDULE_ID == p.FCRAFT_SCHEDULE_ID).ToList();

                if (!bookings.Any())
                {
                    p.FCRAFT_STATUS = "unstart"; // 未开工
                }
                else if (bookings.Any(k => k.FWORK_STATUS == "working"))
                {
                    p.FCRAFT_STATUS = "working"; // 加工中
                }
                else if (bookings.Any(k => k.FWORK_STATUS == "paused"))
                {
                    p.FCRAFT_STATUS = "paused"; // 暂停中
                }
                else
                {
                    // 到这里，所有任务只可能是 finished 或 cancel 状态
                    var sumnum = bookings
                        .Where(s => s.FWORK_STATUS == "finished") // 只统计已完工的
                        .Sum(s => (s.FPASS_QTY + s.FNG_QTY));

                    // 将判断条件从 > 修改为 >=
                    if (sumnum >= p.FPLAN_QTY)
                    {
                        p.FCRAFT_STATUS = "finished"; // 已完工 (包括超产的情况)
                    }
                    else if (sumnum > 0)
                    {
                        p.FCRAFT_STATUS = "partfinished"; // 部分完工
                    }
                    else
                    {
                        // 如果已完工数量为0，且没有进行中或暂停中的任务，则认为是"已取消"或"未开始"
                        // 如果所有 booking 都是 cancel 状态，则为 cancel，否则为 unstart (例如只有一个空的开工记录被取消了)
                        if (bookings.All(b => b.FWORK_STATUS == "cancel"))
                        {
                            p.FCRAFT_STATUS = "cancel";
                        }
                        else
                        {
                            p.FCRAFT_STATUS = "unstart";
                        }
                    }
                }
            });


        }

    }
}
