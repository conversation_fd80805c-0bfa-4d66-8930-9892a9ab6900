# 拆分完工取消修复总结

## 修复的问题

### 问题1：取消报工时没有删除拆分排程并合并数量

**原问题**：
- `BatchFinishJobWithSplitAsync` 进行部分拆分时会创建新的拆分排程并减少原始排程数量
- `CancelCompletion` 方法没有正确识别和处理这些拆分排程
- 取消完工后，拆分排程仍然存在，原始排程数量没有恢复

**修复方案**：
1. **精确分类处理**：
   - **拆分排程**：同工艺的拆分排程（`FWORK_ORDER_CRAFT_ID` 相同）
   - **下游子排程**：不同工艺的下游任务（`FWORK_ORDER_CRAFT_ID` 不同）
   - **复用排程**：没有 `FPARENT_CRAFT_SCHEDULE_ID` 的排程

2. **数量合并**：将拆分排程的数量合并回原始排程
3. **数量归还**：将下游子排程的数量归还给下游父排程
4. **完整删除**：删除所有相关的拆分排程和状态记录

### 问题2：完工时产生0数量的下游排程

**原问题**：
- 当完工数量等于下游父排程的计划数量时，会创建0数量的下游排程
- 这导致数据不一致和后续处理问题

**修复方案**：
1. **数量检查**：在创建下游任务前检查父排程剩余数量
2. **跳过0数量**：当父排程数量为0时跳过创建下游任务
3. **数量限制**：当需要数量超过父排程剩余数量时，只使用剩余数量

## 修复的方法

### 1. CancelCompletion 方法
- 修复拆分排程查找和分类逻辑
- 添加拆分排程数量合并逻辑
- 正确处理不同类型的排程删除

### 2. PrepareSplitFinishedScheduleAsync 方法
- 添加 `sourceJobBookingId` 参数
- 正确设置 `FSOURCE_JOB_BOOKING_ID` 字段

### 3. CreateSplitFinishedScheduleAsync 方法
- 添加 `sourceJobBookingId` 参数
- 正确设置 `FSOURCE_JOB_BOOKING_ID` 字段

### 4. PrepareDownstreamChildScheduleAsync 方法
- 修复 `FSOURCE_JOB_BOOKING_ID` 继承逻辑
- 从排程ID改为继承来源报工ID

### 5. CreateDownstreamChildScheduleAsync 方法
- 修复 `FSOURCE_JOB_BOOKING_ID` 继承逻辑

### 6. BatchCreateDownstreamTasksWithPassQtyAsync 方法
- 添加数量检查，避免创建0数量排程
- 优化下游任务创建逻辑

## 关键修复点

### 数据关系修复
```csharp
// 修复前：错误的字段设置
FSOURCE_JOB_BOOKING_ID = sourceSchedule.FCRAFT_SCHEDULE_ID, // 错误：设置为排程ID

// 修复后：正确的字段设置
FSOURCE_JOB_BOOKING_ID = sourceJobBookingId, // 正确：设置为报工ID
```

### 分类逻辑修复
```csharp
// 通过工艺ID判断是拆分排程还是下游子排程
if (schedule.FWORK_ORDER_CRAFT_ID == origSch.FWORK_ORDER_CRAFT_ID)
{
    // 同工艺：拆分排程
    splitSchedulesToDelete.Add(schedule);
}
else
{
    // 不同工艺：下游子排程
    childSchedulesToDelete.Add(schedule);
}
```

### 数量检查修复
```csharp
// 检查父排程是否有足够的数量，避免创建0数量的下游排程
var currentParentQty = parentsToUpdate.ContainsKey(parentSchedule.FCRAFT_SCHEDULE_ID) 
    ? parentsToUpdate[parentSchedule.FCRAFT_SCHEDULE_ID].FPLAN_QTY 
    : parentSchedule.FPLAN_QTY;

if (currentParentQty <= 0)
{
    // 父排程已经没有剩余数量，跳过创建下游任务
    continue;
}
```

## 测试建议

### 场景1：部分拆分完工后取消
1. 执行 `BatchFinishJobWithSplitAsync` 进行部分拆分（如原排程100，拆分30）
2. 验证原排程数量变为70，拆分排程数量为30
3. 执行 `CancelCompletion` 取消完工
4. 验证拆分排程被删除，原排程数量恢复为100

### 场景2：完工数量等于下游父排程数量
1. 执行完工操作，完工数量等于下游父排程数量
2. 验证不会创建0数量的下游排程
3. 验证下游父排程被正确复用或更新

### 场景3：拆分完工创建下游任务后取消
1. 执行 `BatchFinishJobWithSplitAsync` 进行拆分
2. 验证下游任务的 `FSOURCE_JOB_BOOKING_ID` 正确继承
3. 执行 `CancelCompletion` 取消完工
4. 验证所有相关的拆分排程和下游任务都被正确处理

## 修复效果

✅ **取消完工时**：
- 正确查找并删除所有拆分排程
- 将拆分排程的数量合并回原始排程
- 正确处理下游任务的数量归还

✅ **完工时**：
- 不再创建0数量的下游排程
- 正确处理父排程数量不足的情况
- 优化下游任务创建逻辑

✅ **数据一致性**：
- 拆分排程和下游任务都正确设置来源报工ID
- `CancelCompletion` 方法能够通过 `FSOURCE_JOB_BOOKING_ID` 正确查找并处理所有相关数据
- 字段含义明确：`FSOURCE_JOB_BOOKING_ID` 存储报工任务ID，而不是排程ID
