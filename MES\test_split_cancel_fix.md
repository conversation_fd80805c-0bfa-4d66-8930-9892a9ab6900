# 拆分完工取消修复验证

## 修复内容

### 问题描述
`BatchFinishJobWithSplitAsync` 方法生成的拆分数据在 `CancelCompletion` 方法中没有被正确考虑到，主要原因是拆分排程的 `FSOURCE_JOB_BOOKING_ID` 字段没有被正确设置。

### 修复的方法

1. **PrepareSplitFinishedScheduleAsync** - 添加 `sourceJobBookingId` 参数并设置 `FSOURCE_JOB_BOOKING_ID` 字段
2. **CreateSplitFinishedScheduleAsync** - 添加 `sourceJobBookingId` 参数并设置 `FSOURCE_JOB_BOOKING_ID` 字段
3. **PrepareDownstreamChildScheduleAsync** - 修复 `FSOURCE_JOB_BOOKING_ID` 字段，从排程ID改为继承来源报工ID
4. **CreateDownstreamChildScheduleAsync** - 修复 `FSOURCE_JOB_BOOKING_ID` 字段，从排程ID改为继承来源报工ID

### 修复后的数据流

1. **拆分完工时**：
   - 创建拆分排程时，`FSOURCE_JOB_BOOKING_ID` = 触发拆分的报工ID
   - 创建下游任务时，`FSOURCE_JOB_BOOKING_ID` = 继承自拆分排程的来源报工ID

2. **取消完工时**：
   - `CancelCompletion` 方法能够通过 `FSOURCE_JOB_BOOKING_ID` 正确查找到拆分排程
   - 正确删除子排程并恢复父排程数量

### 验证要点

1. **拆分排程创建**：
   ```sql
   -- 验证拆分排程的 FSOURCE_JOB_BOOKING_ID 是否正确设置
   SELECT FCRAFT_SCHEDULE_ID, FCRAFT_SCHEDULE_NO, FSOURCE_JOB_BOOKING_ID, FPARENT_CRAFT_SCHEDULE_ID
   FROM T_MESD_CRAFT_SCHEDULE 
   WHERE FSOURCE_JOB_BOOKING_ID IS NOT NULL
   ```

2. **取消完工查询**：
   ```sql
   -- 验证取消完工时能否正确查找到拆分排程
   SELECT * FROM T_MESD_CRAFT_SCHEDULE 
   WHERE FSOURCE_JOB_BOOKING_ID IN ('报工ID列表') 
   AND FPARENT_CRAFT_SCHEDULE_ID IS NOT NULL
   ```

3. **下游任务创建**：
   ```sql
   -- 验证下游任务的 FSOURCE_JOB_BOOKING_ID 是否正确继承
   SELECT s1.FCRAFT_SCHEDULE_NO as '拆分排程', s1.FSOURCE_JOB_BOOKING_ID as '拆分排程来源',
          s2.FCRAFT_SCHEDULE_NO as '下游任务', s2.FSOURCE_JOB_BOOKING_ID as '下游任务来源'
   FROM T_MESD_CRAFT_SCHEDULE s1
   JOIN T_MESD_CRAFT_SCHEDULE s2 ON s1.FCRAFT_SCHEDULE_ID = s2.FPARENT_CRAFT_SCHEDULE_ID
   WHERE s1.FSOURCE_JOB_BOOKING_ID IS NOT NULL
   ```

### 测试场景

1. **场景1：部分拆分完工**
   - 执行 `BatchFinishJobWithSplitAsync` 进行部分拆分
   - 验证拆分排程的 `FSOURCE_JOB_BOOKING_ID` 正确设置
   - 执行 `CancelCompletion` 取消完工
   - 验证拆分排程被正确删除，父排程数量被恢复

2. **场景2：拆分完工后创建下游任务**
   - 执行 `BatchFinishJobWithSplitAsync` 进行拆分
   - 验证下游任务的 `FSOURCE_JOB_BOOKING_ID` 正确继承
   - 执行 `CancelCompletion` 取消完工
   - 验证所有相关的拆分排程和下游任务都被正确处理

3. **场景3：复用父排程的情况**
   - 执行完工操作复用父排程
   - 验证父排程的 `FSOURCE_JOB_BOOKING_ID` 正确设置
   - 执行 `CancelCompletion` 取消完工
   - 验证父排程的 `FSOURCE_JOB_BOOKING_ID` 被正确清空

## 关键修复点总结

1. **字段含义明确**：`FSOURCE_JOB_BOOKING_ID` 应该存储报工任务ID，而不是排程ID
2. **数据一致性**：拆分排程和下游任务都应该正确设置来源报工ID
3. **取消逻辑完整**：`CancelCompletion` 方法能够通过 `FSOURCE_JOB_BOOKING_ID` 正确查找并处理所有相关数据
